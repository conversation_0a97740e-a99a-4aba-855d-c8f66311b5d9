#!/usr/bin/env node

/**
 * Forest MCP Server Entry Point
 *
 * This script starts the main Forest.os MCP server located in the forest-server directory.
 * All core functionality has been consolidated into the forest-server structure.
 */

console.error('DEBUG: start-server.mjs starting...');
console.error('DEBUG: Current working directory:', process.cwd());
console.error('DEBUG: Script location:', import.meta.url);

// Change to the forest-server directory to ensure relative paths work correctly
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const forestServerDir = join(__dirname, 'forest-server');

console.error('DEBUG: Changing to forest-server directory:', forestServerDir);
process.chdir(forestServerDir);
console.error('DEBUG: New working directory:', process.cwd());

import('./___stage1/core-initialization.js')
  .then(async ({ CoreInitialization }) => {
    console.error('DEBUG: Stage1 core-initialization imported successfully');
    const initializer = new CoreInitialization();
    console.error('DEBUG: Running Stage1 initialization...');
    await initializer.initialize();
    console.error('Forest MCP Stage1 server is up and running!');
  })
  .catch(error => {
    console.error('FATAL ERROR in start-server.mjs:', error.message);
    console.error('Stack:', error.stack);
    console.error('Process will exit with code 1');
    process.exit(1);
  });
