This Product Requirements Document (PRD) outlines the specifications for the Forest system, a personal project and life management tool. The primary objective is to define a simplified, robust, and efficient architecture that preserves the core "magic" of its Hierarchical Task Analysis (HTA) and strategy evolution capabilities. This document details the system's guiding principles, architecture, core functionalities, and the specific success criteria required to validate its successful implementation.

1.0 Introduction
1.1 Purpose
This document provides a comprehensive overview of the Forest system's requirements. It serves as the single source of truth for development, guiding the consolidation and simplification of the existing codebase while ensuring that its most critical and sophisticated features—specifically the HTA Tree generation and the Strategy Evolution logic—remain fully intact and functional.

1.2 Vision
The Forest system is designed to be an intelligent and adaptive partner in achieving personal and professional goals. It moves beyond simple task management by dynamically creating and evolving project plans based on user interaction, learning outcomes, and real-world events. The "magic" of the system lies in its ability to understand the complexity of a goal, structure a strategic path forward, and adapt that path as the user learns and grows. This simplification effort aims to make the underlying system more maintainable, scalable, and efficient without sacrificing this core intelligence.

2.0 Guiding Principles
The following principles are foundational to this project and must be adhered to throughout the development and refactoring process.

2.1 Preserve the HTA Magic
The HTA (Hierarchical Task Analysis) functionality is the "crown jewel" of the Forest system. Its ability to analyze a goal's complexity, generate adaptive tree structures with strategic branches, create sophisticated fallback tasks, and integrate with AI (Claude) for task generation is non-negotiable. All consolidation efforts must treat this logic as sacred, ensuring every function and piece of intelligence is preserved.

2.2 Protect the Strategy Evolver
The evolve_strategy function and its underlying module are essential to the core feedback loop of the system. This component enables dynamic adaptation to learning, user feedback, and significant life changes. Its capabilities for handling breakthroughs by escalating complexity and detecting new opportunities must be fully maintained. The system's power comes from its ability not just to plan, but to evolve.

2.3 Radical Simplification with Zero Intelligence Loss
The primary goal is to significantly reduce the system's complexity and codebase size, targeting the elimination of approximately 70% of non-essential modules. However, this simplification cannot come at the cost of the core intelligence. The process involves aggressive consolidation and elimination of over-engineered or non-critical components while carefully merging and preserving the essential logic within a more streamlined architecture.

3.0 System Architecture
The simplified architecture will be organized as follows, ensuring all specified file size constraints are met while preserving the designated core logic.

3.1 Directory Structure

forest-server/
├── core-server.js           (<750 lines) - Main MCP server
├── core-handlers.js         (<750 lines) - Handlers for the 12 core tools
├── core-initialization.js   (<750 lines) - Startup and initialization logic
├── modules/
│   ├── hta-core.js              (<750 lines) - 🧠 ALL HTA MAGIC PRESERVED
│   ├── task-strategy-core.js    (<750 lines) - Task intelligence + Strategy evolution
│   ├── core-intelligence.js     (<750 lines) - Reasoning, identity, and core AI logic
│   ├── mcp-core.js              (<750 lines) - MCP handlers & internal routing
│   ├── data-persistence.js      (<500 lines) - Simplified data persistence layer
│   ├── project-management.js    (<400 lines) - Core project creation and management
│   ├── memory-sync.js           (219 lines) - Context and memory integration
│   └── constants.js             (<200 lines) - Essential system-wide constants
├── utils/
│   └── hta-hierarchy-utils.js   (Keep as-is) - Standalone hierarchy intelligence utility
├── models/                      (Keep existing) - Data models for projects, tasks, etc.
└── __tests__/                   (Keep core tests) - Tests for the core loop, HTA, and strategy evolution
3.2 Module Descriptions & Consolidation Logic

hta-core.js: This is the new, consolidated home for the system's core HTA magic.

Source Files Merged: hta-tree-builder.js (1,151 lines) + hta-bridge.js (507 lines).

Preserved Intelligence:

Complexity Analysis: analyzeGoalComplexity() logic to determine tree structure.

Adaptive Architecture: calculateTreeStructure() logic for adaptive tree generation.

Strategic Branch Generation: deriveStrategicBranches() to create meaningful learning paths (e.g., Foundation, Research, Capability, Implementation, Mastery).

Hierarchical Task Decomposition: The fundamental logic for breaking down goals.

Claude Integration & Fallbacks: generateHTAData() for AI task generation and createFallbackHTA() with generateSkeletonTasks() for sophisticated backups when AI is unavailable.

Frontier Node Management: transformTasksToFrontierNodes() to format tasks for the intelligence engine.

Tree Structure Optimization: All underlying logic that ensures a valid and effective HTA tree.

task-strategy-core.js: This module combines task selection intelligence with the critical strategy evolution logic.

Source Files Merged: task-intelligence.js (1,134 lines) + strategy-evolver.js (445 lines).

Preserved Intelligence:

HTA-Aware Task Selection: Uses HTA frontier nodes to intelligently select the next optimal task.

Learning-Driven Evolution: evolveHTABasedOnLearning() to adapt the HTA tree based on task completion outcomes.

Breakthrough Handling: handleBreakthrough() logic to escalate task complexity after a user success.

Opportunity Detection: handleOpportunityDetection() to expand the HTA tree when new opportunities are identified.

Life Adaptation: Adapts the strategy based on major life changes (financial, health, location).

Adaptive Task Creation: generateFollowUpTasks() and generateBreakthroughTasks() to dynamically add new, relevant tasks to the tree.

core-intelligence.js: Consolidates high-level reasoning.

Source Files Merged: reasoning-engine.js (1,021 lines).

Function: Provides the core reasoning capabilities for making strategic decisions and managing system identity.

project-management.js: Handles the lifecycle of projects.

Function: Contains the logic for create_project, switch_project, and list_projects.

hta-hierarchy-utils.js: A critical utility kept separate for focused hierarchy management.

Function: Provides essential utility functions like buildParentMap, getLeafTasks, and validateHierarchy to ensure the structural integrity of the HTA tree.

3.3 Eliminated Modules
The following modules will be eliminated to achieve the simplification goals: analytics-tools.js, data-archiver.js, proactive-insights-handler.js, finance-bridge.js, cache-cleaner.js, hta-debug-tools.js, task-quality-verifier.js, and system-clock.js. Their functionalities are considered non-core, over-engineered, or can be absorbed elsewhere in a simplified form.

4.0 Core Functionality (Tools)
The system will expose twelve core tools (functions) through the core-handlers.js module.

create_project - Project Creation & Configuration: Initializes a new project space, setting up the necessary data structures for HTA and tracking.

switch_project - Project Management: Sets the active project context for all subsequent operations.

list_projects - Basic Project Listing: Lists all available projects for the user.

build_hta_tree - HTA Tree Building & Task Generation: The primary entry point for the "HTA Magic." Takes a high-level goal and uses the hta-core.js module to perform complexity analysis, generate the strategic branches, and build the initial hierarchical task tree, integrating with Claude where possible.

get_hta_status - HTA Diagnostics: Provides the status and key intelligence metrics of the current HTA tree, such as completion percentage, number of frontier nodes, and current complexity level.

get_next_task - Task Intelligence & Selection: The main function for user progression. It queries the task-strategy-core.js module, which uses the HTA frontier intelligence to determine and return the next most optimal task for the user to work on.

complete_block - Task Completion & Learning History: The user calls this upon completing a task. It logs the completion data (e.g., actual time vs. estimated), which feeds directly into the learning mechanisms of the strategy evolver.

evolve_strategy - Strategy Evolution & Adaptive Learning (⭐ CRITICAL): The core adaptive function. It can be triggered automatically by events (like task completion) or manually by the user providing direct feedback. It uses the task-strategy-core.js module to analyze performance and feedback, potentially triggering HTA evolution, breakthrough escalation, life change adaptation, or new opportunity detection.

current_status - Progress Tracking & Analytics: Provides a high-level overview of the user's progress within the current project, including metrics derived from the HTA status.

generate_daily_schedule - Daily Optimization: Uses the current HTA's task complexity and priorities to suggest a daily work schedule, allocating time effectively.

sync_forest_memory - Context Management & Memory Sync: Ensures the state of the current HTA, project context, and user progress is persistently saved and can be reloaded across sessions.

ask_truthful_claude - Error Handling & Recovery: A utility function to interface with Claude for troubleshooting, clarification, or handling unexpected errors within the system.

5.0 HTA & Strategy Integration Points
The magic of the system emerges from the tight integration of the HTA structure with other core components.

Task Intelligence: get_next_task directly consumes the HTA frontier nodes managed by hta-core.js.

Strategy Evolver: evolve_strategy directly modifies the HTA tree in hta-core.js based on learning outcomes.

Completion System: complete_block provides the raw data (actual vs. estimated) that informs the HTA evolution logic in task-strategy-core.js.

Memory Sync: sync_forest_memory is responsible for serializing and deserializing the entire HTA context to preserve it across sessions.

Daily Scheduling: generate_daily_schedule uses HTA task metadata (e.g., complexity, dependencies) to allocate user time effectively.

6.0 Success Criteria
The project will be considered a success only if all the following criteria are met, verifying that the system's core intelligence has been preserved perfectly within the new, simplified architecture.

6.1 HTA Intelligence Tests

Complexity Analysis: A given goal correctly maps to a complexity score, which in turn generates a tree structure of the appropriate size and depth. This must work identically to the pre-consolidation logic.

Strategic Branches: The generated HTA tree must contain the correct strategic branches: Foundation → Research → Capability → Implementation → Mastery.

Adaptive Evolution: A sequence of completed tasks must result in the generation of new, relevant follow-up tasks based on the defined learning-driven evolution logic.

Breakthrough Escalation: Successfully completing a series of challenging tasks correctly triggers the handleBreakthrough logic, resulting in the generation of harder, more complex tasks.

Frontier Management: The system must always accurately identify the set of actionable, next-optimal tasks (the frontier).

Claude Integration: The system successfully calls the Claude API to generate tasks when appropriate and available.

Fallback Intelligence: When the Claude API is unavailable or fails, the system correctly generates the sophisticated, predefined skeleton tasks as a fallback.

6.2 Strategy Evolution Requirements

evolve_strategy Tool: The tool must be fully functional and callable by the user with feedback.

StrategyEvolver Module Logic: The core logic from the original strategy-evolver.js must be preserved and fully operational within task-strategy-core.js.

Breakthrough Handling: The escalation mechanism must function as designed.

Life Adaptation: The system must correctly adapt the HTA and overall strategy in response to simulated life changes (financial, health, etc.).

HTA Evolution: The dynamic task generation and modification of the HTA tree must work correctly.

Event Integration: The simplified event-driven flow (e.g., task completion triggering evolution) must be functional.

6.3 Core Loop Integration Tests

Component #5: The "Strategy Evolution & Adaptive Learning" component must be fully operational within the main application loop.

Task Completion → Evolution: A verified end-to-end test must show that calling complete_block correctly triggers the strategy evolution logic.

User Feedback → Adaptation: A verified end-to-end test must show that calling evolve_strategy with user feedback correctly adapts the HTA.

Context Changes → Updates: Changes in the user's context (handled via memory sync) must be correctly reflected in the active HTA tree upon reload.

6.4 Magic Preservation Guarantee

Same Intelligence: The output of analyzeGoalComplexity and calculateTreeStructure must be identical to the original implementation for a given set of inputs.

Same Evolution: The learning-driven adaptation (evolveHTABasedOnLearning) must produce the same new tasks and HTA modifications as the original.

Same Strategic Framework: All strategic branches must be preserved without modification to their logic.

Same Task Quality: The intelligence behind generating both AI-driven and skeleton tasks must be maintained.

Same Adaptive Power: The handling of breakthroughs and opportunities must be functionally identical to the original system.