/**
 * DEPRECATED: reasoning-engine.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/core-intelligence.js
 * - New Class: CoreIntelligence
 * - Migration Note: Reasoning engine optimized and moved to CoreIntelligence class
 * 
 * @deprecated Use CoreIntelligence from ___stage1/modules/core-intelligence.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'ReasoningEngine') {
  const message = `DEPRECATED: ${functionName} from reasoning-engine.js has been consolidated.\n` +
                 `Please use CoreIntelligence from ___stage1/modules/core-intelligence.js instead.\n` +
                 `Migration: Reasoning engine optimized and moved to CoreIntelligence class`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'reasoning-engine.js',
    newLocation: '___stage1/modules/core-intelligence.js',
    newClass: 'CoreIntelligence'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const ReasoningEngine = () => deprecated('ReasoningEngine');
export { ReasoningEngine as default };
