/**
 * DEPRECATED: hta-bridge.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/hta-core.js
 * - New Class: HtaCore
 * - Migration Note: HTA bridge functionality integrated into HtaCore class
 * 
 * @deprecated Use HtaCore from ___stage1/modules/hta-core.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'HtaBridge') {
  const message = `DEPRECATED: ${functionName} from hta-bridge.js has been consolidated.\n` +
                 `Please use HtaCore from ___stage1/modules/hta-core.js instead.\n` +
                 `Migration: HTA bridge functionality integrated into HtaCore class`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'hta-bridge.js',
    newLocation: '___stage1/modules/hta-core.js',
    newClass: 'HtaCore'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const HtaBridge = () => deprecated('HtaBridge');
export { HtaBridge as default };
