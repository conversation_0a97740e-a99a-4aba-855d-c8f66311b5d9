/**
 * DEPRECATED: constants.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/memory-sync.js
 * - New Class: MemorySync (constants exported)
 * - Migration Note: Constants moved to memory-sync.js as named exports
 * 
 * @deprecated Use MemorySync (constants exported) from ___stage1/modules/memory-sync.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'Constants') {
  const message = `DEPRECATED: ${functionName} from constants.js has been consolidated.\n` +
                 `Please use MemorySync (constants exported) from ___stage1/modules/memory-sync.js instead.\n` +
                 `Migration: Constants moved to memory-sync.js as named exports`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'constants.js',
    newLocation: '___stage1/modules/memory-sync.js',
    newClass: 'MemorySync (constants exported)'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const Constants = () => deprecated('Constants');
export { Constants as default };
