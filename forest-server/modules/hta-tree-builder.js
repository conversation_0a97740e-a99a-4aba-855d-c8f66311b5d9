/**
 * DEPRECATED: hta-tree-builder.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/hta-core.js
 * - New Class: HtaCore
 * - Migration Note: HTA tree building functionality moved to HtaCore class
 * 
 * @deprecated Use HtaCore from ___stage1/modules/hta-core.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'HtaTreeBuilder') {
  const message = `DEPRECATED: ${functionName} from hta-tree-builder.js has been consolidated.\n` +
                 `Please use HtaCore from ___stage1/modules/hta-core.js instead.\n` +
                 `Migration: HTA tree building functionality moved to HtaCore class`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'hta-tree-builder.js',
    newLocation: '___stage1/modules/hta-core.js',
    newClass: 'HtaCore'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const HtaTreeBuilder = () => deprecated('HtaTreeBuilder');
export { HtaTreeBuilder as default };
