/**
 * DEPRECATED: tool-router.js
 * This module has been removed in the Stage1 consolidation.
 * 
 * This functionality was determined to be non-essential for the core Forest system.
 * If you need this functionality, please:
 * 1. Check if equivalent functionality exists in the consolidated modules
 * 2. Implement as a custom extension if truly needed
 * 3. Consider if the functionality is actually necessary
 * 
 * @deprecated Module removed in Stage1 consolidation
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'ToolRouter') {
  const message = `DEPRECATED: ${functionName} from tool-router.js has been removed.\n` +
                 `This module was eliminated during Stage1 consolidation as non-essential.\n` +
                 `Please check consolidated modules for equivalent functionality.`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'tool-router.js',
    reason: 'eliminated_in_stage1_consolidation'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const ToolRouter = () => deprecated('ToolRouter');
export { ToolRouter as default };
