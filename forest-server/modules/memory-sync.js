/**
 * DEPRECATED: memory-sync.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/memory-sync.js
 * - New Class: MemorySync
 * - Migration Note: Memory sync functionality preserved with constants integration
 * 
 * @deprecated Use MemorySync from ___stage1/modules/memory-sync.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'MemorySync') {
  const message = `DEPRECATED: ${functionName} from memory-sync.js has been consolidated.\n` +
                 `Please use MemorySync from ___stage1/modules/memory-sync.js instead.\n` +
                 `Migration: Memory sync functionality preserved with constants integration`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'memory-sync.js',
    newLocation: '___stage1/modules/memory-sync.js',
    newClass: 'MemorySync'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const MemorySync = () => deprecated('MemorySync');
export { MemorySync as default };
