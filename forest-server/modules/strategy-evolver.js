/**
 * DEPRECATED: strategy-evolver.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/task-strategy-core.js
 * - New Class: TaskStrategyCore
 * - Migration Note: Strategy evolution integrated into TaskStrategyCore class
 * 
 * @deprecated Use TaskStrategyCore from ___stage1/modules/task-strategy-core.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'StrategyEvolver') {
  const message = `DEPRECATED: ${functionName} from strategy-evolver.js has been consolidated.\n` +
                 `Please use TaskStrategyCore from ___stage1/modules/task-strategy-core.js instead.\n` +
                 `Migration: Strategy evolution integrated into TaskStrategyCore class`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'strategy-evolver.js',
    newLocation: '___stage1/modules/task-strategy-core.js',
    newClass: 'TaskStrategyCore'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const StrategyEvolver = () => deprecated('StrategyEvolver');
export { StrategyEvolver as default };
