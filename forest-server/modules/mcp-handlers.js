/**
 * DEPRECATED: mcp-handlers.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/mcp-core.js
 * - New Class: McpCore
 * - Migration Note: MCP handlers consolidated into McpCore class
 * 
 * @deprecated Use McpCore from ___stage1/modules/mcp-core.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'McpHandlers') {
  const message = `DEPRECATED: ${functionName} from mcp-handlers.js has been consolidated.\n` +
                 `Please use McpCore from ___stage1/modules/mcp-core.js instead.\n` +
                 `Migration: MCP handlers consolidated into McpCore class`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'mcp-handlers.js',
    newLocation: '___stage1/modules/mcp-core.js',
    newClass: 'McpCore'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const McpHandlers = () => deprecated('McpHandlers');
export { McpHandlers as default };
