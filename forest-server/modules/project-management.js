/**
 * DEPRECATED: project-management.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/project-management.js
 * - New Class: ProjectManagement
 * - Migration Note: Project management optimized but API preserved
 * 
 * @deprecated Use ProjectManagement from ___stage1/modules/project-management.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'ProjectManagement') {
  const message = `DEPRECATED: ${functionName} from project-management.js has been consolidated.\n` +
                 `Please use ProjectManagement from ___stage1/modules/project-management.js instead.\n` +
                 `Migration: Project management optimized but API preserved`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'project-management.js',
    newLocation: '___stage1/modules/project-management.js',
    newClass: 'ProjectManagement'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const ProjectManagement = () => deprecated('ProjectManagement');
export { ProjectManagement as default };
