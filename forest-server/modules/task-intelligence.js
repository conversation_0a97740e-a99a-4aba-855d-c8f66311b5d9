/**
 * DEPRECATED: task-intelligence.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/task-strategy-core.js
 * - New Class: TaskStrategyCore
 * - Migration Note: Task intelligence moved to TaskStrategyCore class
 * 
 * @deprecated Use TaskStrategyCore from ___stage1/modules/task-strategy-core.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'TaskIntelligence') {
  const message = `DEPRECATED: ${functionName} from task-intelligence.js has been consolidated.\n` +
                 `Please use TaskStrategyCore from ___stage1/modules/task-strategy-core.js instead.\n` +
                 `Migration: Task intelligence moved to TaskStrategyCore class`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'task-intelligence.js',
    newLocation: '___stage1/modules/task-strategy-core.js',
    newClass: 'TaskStrategyCore'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const TaskIntelligence = () => deprecated('TaskIntelligence');
export { TaskIntelligence as default };
