/**
 * DEPRECATED: data-persistence.js
 * This module has been consolidated into the Stage1 architecture.
 * 
 * Migration Information:
 * - New Location: ___stage1/modules/data-persistence.js
 * - New Class: DataPersistence
 * - Migration Note: Data persistence optimized but API preserved
 * 
 * @deprecated Use DataPersistence from ___stage1/modules/data-persistence.js instead
 */

import { logger } from './utils/logger.js';

export function deprecated(functionName = 'DataPersistence') {
  const message = `DEPRECATED: ${functionName} from data-persistence.js has been consolidated.\n` +
                 `Please use DataPersistence from ___stage1/modules/data-persistence.js instead.\n` +
                 `Migration: Data persistence optimized but API preserved`;
  
  logger.warn('[DEPRECATED]', { 
    module: 'data-persistence.js',
    newLocation: '___stage1/modules/data-persistence.js',
    newClass: 'DataPersistence'
  });
  
  throw new Error(message);
}

// Export deprecated function as default for backward compatibility
export default deprecated;

// Common class/function names that might be imported
export const DataPersistence = () => deprecated('DataPersistence');
export { DataPersistence as default };
