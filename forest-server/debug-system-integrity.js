/**
 * Forest.os System Integrity Debugger & Super Glue
 * 
 * CTO-Level Comprehensive Debugging System:
 * 1. Traces dependencies and identifies brittleness
 * 2. Detects circular dependencies and module coupling issues
 * 3. Validates all inter-module connections
 * 4. Provides "super glue" to strengthen weak connections
 * 5. Implements fail-safe mechanisms and graceful degradation
 * 6. Real-time monitoring and auto-healing capabilities
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { getForestLogger } from './modules/winston-logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const logger = getForestLogger({ module: 'SystemIntegrityDebugger' });

class SystemIntegrityDebugger {
  constructor() {
    this.dependencyGraph = new Map();
    this.circularDependencies = [];
    this.brittleConnections = [];
    this.moduleHealth = new Map();
    this.errorTraces = [];
    this.superGlueConnections = new Map();
    this.healingStrategies = new Map();
    
    // Initialize healing strategies
    this.initializeHealingStrategies();
  }

  /**
   * Initialize auto-healing strategies for common failure patterns
   */
  initializeHealingStrategies() {
    this.healingStrategies.set('MODULE_IMPORT_FAILURE', {
      detect: (error) => error.code === 'ERR_MODULE_NOT_FOUND',
      heal: async (error, context) => {
        logger.warn('Module import failure detected, attempting recovery', { error: error.message, context });
        return await this.createModuleFallback(error, context);
      }
    });

    this.healingStrategies.set('CIRCULAR_DEPENDENCY', {
      detect: (error) => error.message.includes('circular') || error.message.includes('cyclic'),
      heal: async (error, context) => {
        logger.warn('Circular dependency detected, implementing lazy loading', { error: error.message, context });
        return await this.resolveCircularDependency(error, context);
      }
    });

    this.healingStrategies.set('ASYNC_INITIALIZATION_FAILURE', {
      detect: (error) => error.message.includes('async') && error.message.includes('initialization'),
      heal: async (error, context) => {
        logger.warn('Async initialization failure, implementing retry with backoff', { error: error.message, context });
        return await this.retryAsyncInitialization(error, context);
      }
    });

    this.healingStrategies.set('DEPENDENCY_INJECTION_FAILURE', {
      detect: (error) => error.message.includes('undefined') && error.message.includes('constructor'),
      heal: async (error, context) => {
        logger.warn('Dependency injection failure, creating proxy dependencies', { error: error.message, context });
        return await this.createProxyDependencies(error, context);
      }
    });
  }

  /**
   * Comprehensive system analysis
   */
  async analyzeSystemIntegrity() {
    logger.info('Starting comprehensive system integrity analysis');
    
    const analysis = {
      timestamp: new Date().toISOString(),
      dependencyAnalysis: await this.analyzeDependencies(),
      circularDependencyCheck: await this.detectCircularDependencies(),
      moduleHealthCheck: await this.checkModuleHealth(),
      brittlenessAssessment: await this.assessBrittleness(),
      errorTraceAnalysis: await this.analyzeErrorTraces(),
      superGlueRecommendations: await this.generateSuperGlueRecommendations(),
      healingCapabilities: this.getHealingCapabilities()
    };

    await this.generateIntegrityReport(analysis);
    return analysis;
  }

  /**
   * Analyze module dependencies and create dependency graph
   */
  async analyzeDependencies() {
    logger.info('Analyzing module dependencies');
    
    const modulesDir = path.join(__dirname, 'modules');
    const moduleFiles = await this.findJavaScriptFiles(modulesDir);
    
    for (const file of moduleFiles) {
      const dependencies = await this.extractDependencies(file);
      const moduleName = path.relative(modulesDir, file);
      this.dependencyGraph.set(moduleName, dependencies);
    }

    // Analyze main server dependencies
    const serverFile = path.join(__dirname, 'server-modular.js');
    const serverDependencies = await this.extractDependencies(serverFile);
    this.dependencyGraph.set('server-modular.js', serverDependencies);

    return {
      totalModules: this.dependencyGraph.size,
      dependencyGraph: Object.fromEntries(this.dependencyGraph),
      highestCoupling: this.findHighestCoupledModules(),
      isolatedModules: this.findIsolatedModules()
    };
  }

  /**
   * Extract dependencies from a JavaScript file
   */
  async extractDependencies(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const dependencies = {
        staticImports: [],
        dynamicImports: [],
        requires: [],
        exports: [],
        potentialCircular: []
      };

      // Extract static imports
      const staticImportRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+)?['"`]([^'"`]+)['"`]/g;
      let match;
      while ((match = staticImportRegex.exec(content)) !== null) {
        dependencies.staticImports.push(match[1]);
      }

      // Extract dynamic imports
      const dynamicImportRegex = /(?:await\s+)?import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
      while ((match = dynamicImportRegex.exec(content)) !== null) {
        dependencies.dynamicImports.push(match[1]);
      }

      // Extract requires
      const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
      while ((match = requireRegex.exec(content)) !== null) {
        dependencies.requires.push(match[1]);
      }

      // Extract exports
      const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g;
      while ((match = exportRegex.exec(content)) !== null) {
        dependencies.exports.push(match[1]);
      }

      return dependencies;
    } catch (error) {
      logger.error('Failed to extract dependencies', { filePath, error: error.message });
      return { staticImports: [], dynamicImports: [], requires: [], exports: [], potentialCircular: [] };
    }
  }

  /**
   * Detect circular dependencies
   */
  async detectCircularDependencies() {
    logger.info('Detecting circular dependencies');
    
    const visited = new Set();
    const recursionStack = new Set();
    const cycles = [];

    const dfs = (module, path = []) => {
      if (recursionStack.has(module)) {
        const cycleStart = path.indexOf(module);
        const cycle = path.slice(cycleStart).concat([module]);
        cycles.push(cycle);
        return;
      }

      if (visited.has(module)) return;

      visited.add(module);
      recursionStack.add(module);
      path.push(module);

      const dependencies = this.dependencyGraph.get(module);
      if (dependencies) {
        const allDeps = [
          ...dependencies.staticImports,
          ...dependencies.dynamicImports,
          ...dependencies.requires
        ].filter(dep => dep.startsWith('./') || dep.startsWith('../'));

        for (const dep of allDeps) {
          const normalizedDep = this.normalizeDependencyPath(dep, module);
          if (this.dependencyGraph.has(normalizedDep)) {
            dfs(normalizedDep, [...path]);
          }
        }
      }

      recursionStack.delete(module);
      path.pop();
    };

    for (const module of this.dependencyGraph.keys()) {
      if (!visited.has(module)) {
        dfs(module);
      }
    }

    this.circularDependencies = cycles;
    return {
      detected: cycles.length > 0,
      cycles: cycles,
      count: cycles.length,
      severity: this.assessCircularDependencySeverity(cycles)
    };
  }

  /**
   * Check health of individual modules
   */
  async checkModuleHealth() {
    logger.info('Checking module health');
    
    const healthChecks = new Map();

    for (const [moduleName] of this.dependencyGraph) {
      const health = await this.checkIndividualModuleHealth(moduleName);
      healthChecks.set(moduleName, health);
      this.moduleHealth.set(moduleName, health);
    }

    return {
      totalModules: healthChecks.size,
      healthyModules: Array.from(healthChecks.values()).filter(h => h.status === 'healthy').length,
      unhealthyModules: Array.from(healthChecks.values()).filter(h => h.status !== 'healthy'),
      criticalIssues: Array.from(healthChecks.values()).filter(h => h.severity === 'critical'),
      overallHealth: this.calculateOverallHealth(healthChecks)
    };
  }

  /**
   * Check health of an individual module
   */
  async checkIndividualModuleHealth(moduleName) {
    const health = {
      module: moduleName,
      status: 'healthy',
      severity: 'low',
      issues: [],
      recommendations: []
    };

    try {
      const modulePath = moduleName.includes('server-modular.js') 
        ? path.join(__dirname, 'server-modular.js')
        : path.join(__dirname, 'modules', moduleName);
      
      const content = await fs.readFile(modulePath, 'utf-8');
      
      // Check for common issues
      if (content.includes('console.log') || content.includes('console.error')) {
        health.issues.push('Uses console logging instead of winston logger');
        health.recommendations.push('Replace console.* with winston logger');
      }

      if (content.includes('process.exit')) {
        health.issues.push('Contains process.exit calls');
        health.severity = 'high';
        health.recommendations.push('Replace process.exit with graceful error handling');
      }

      if (!content.includes('try') && !content.includes('catch')) {
        health.issues.push('Lacks error handling');
        health.severity = 'medium';
        health.recommendations.push('Add comprehensive error handling');
      }

      const asyncWithoutAwait = /async\s+function[^{]*{[^}]*(?!await)[^}]*}/g;
      if (asyncWithoutAwait.test(content)) {
        health.issues.push('Async functions without await usage');
        health.recommendations.push('Review async function implementations');
      }

      if (health.issues.length > 0) {
        health.status = health.severity === 'critical' ? 'critical' : 'warning';
      }

    } catch (error) {
      health.status = 'error';
      health.severity = 'critical';
      health.issues.push(`Failed to analyze module: ${error.message}`);
    }

    return health;
  }

  /**
   * Assess system brittleness
   */
  async assessBrittleness() {
    logger.info('Assessing system brittleness');
    
    const brittlenessFactors = {
      hardcodedPaths: await this.findHardcodedPaths(),
      missingErrorHandling: await this.findMissingErrorHandling(),
      synchronousOperations: await this.findSynchronousFileOperations(),
      singlePointsOfFailure: await this.identifySinglePointsOfFailure(),
      uncaughtExceptions: await this.findUncaughtExceptions(),
      resourceLeaks: await this.detectPotentialResourceLeaks()
    };

    const brittlenessScore = this.calculateBrittlenessScore(brittlenessFactors);
    
    return {
      score: brittlenessScore,
      level: this.getBrittlenessLevel(brittlenessScore),
      factors: brittlenessFactors,
      recommendations: this.generateBrittlenessRecommendations(brittlenessFactors)
    };
  }

  /**
   * Generate super glue recommendations
   */
  async generateSuperGlueRecommendations() {
    logger.info('Generating super glue recommendations');
    
    const recommendations = {
      dependencyInjection: await this.recommendDependencyInjection(),
      errorBoundaries: await this.recommendErrorBoundaries(),
      circuitBreakers: await this.recommendCircuitBreakers(),
      retryMechanisms: await this.recommendRetryMechanisms(),
      healthChecks: await this.recommendHealthChecks(),
      gracefulDegradation: await this.recommendGracefulDegradation()
    };

    return recommendations;
  }

  /**
   * Implement super glue connections
   */
  async implementSuperGlue() {
    logger.info('Implementing super glue connections');
    
    // Create dependency injection container
    await this.createDependencyInjectionContainer();
    
    // Implement error boundaries
    await this.implementErrorBoundaries();
    
    // Add circuit breakers
    await this.addCircuitBreakers();
    
    // Implement retry mechanisms
    await this.implementRetryMechanisms();
    
    // Add health check endpoints
    await this.addHealthCheckEndpoints();
    
    // Implement graceful degradation
    await this.implementGracefulDegradation();
    
    logger.info('Super glue implementation completed');
  }

  /**
   * Create dependency injection container
   */
  async createDependencyInjectionContainer() {
    const containerCode = `
/**
 * Dependency Injection Container - Super Glue for Forest.os
 * Provides robust dependency management with automatic healing
 */

export class DependencyContainer {
  constructor() {
    this.dependencies = new Map();
    this.factories = new Map();
    this.singletons = new Map();
    this.healthChecks = new Map();
    this.fallbacks = new Map();
  }

  /**
   * Register a dependency with automatic health checking
   */
  register(name, factory, options = {}) {
    this.factories.set(name, factory);
    
    if (options.healthCheck) {
      this.healthChecks.set(name, options.healthCheck);
    }
    
    if (options.fallback) {
      this.fallbacks.set(name, options.fallback);
    }
    
    if (options.singleton) {
      this.singletons.set(name, null);
    }
  }

  /**
   * Resolve dependency with automatic fallback and healing
   */
  async resolve(name) {
    try {
      // Check if singleton exists
      if (this.singletons.has(name) && this.singletons.get(name)) {
        const instance = this.singletons.get(name);
        if (await this.checkHealth(name, instance)) {
          return instance;
        }
      }

      // Create new instance
      const factory = this.factories.get(name);
      if (!factory) {
        throw new Error(\`Dependency '\${name}' not registered\`);
      }

      const instance = await factory();
      
      // Store singleton if configured
      if (this.singletons.has(name)) {
        this.singletons.set(name, instance);
      }

      return instance;
    } catch (error) {
      // Attempt fallback
      const fallback = this.fallbacks.get(name);
      if (fallback) {
        logger.warn(\`Using fallback for dependency '\${name}'\`, { error: error.message });
        return await fallback();
      }
      
      throw error;
    }
  }

  /**
   * Check health of a dependency
   */
  async checkHealth(name, instance) {
    const healthCheck = this.healthChecks.get(name);
    if (!healthCheck) return true;
    
    try {
      return await healthCheck(instance);
    } catch (error) {
      logger.warn(\`Health check failed for '\${name}'\`, { error: error.message });
      return false;
    }
  }

  /**
   * Get all dependency health statuses
   */
  async getHealthStatus() {
    const status = {};
    
    for (const [name] of this.factories) {
      try {
        const instance = this.singletons.get(name);
        status[name] = instance ? await this.checkHealth(name, instance) : 'not_initialized';
      } catch (error) {
        status[name] = 'error';
      }
    }
    
    return status;
  }
}

// Global container instance
export const container = new DependencyContainer();
`;

    await fs.writeFile(
      path.join(__dirname, 'modules/utils/dependency-container.js'),
      containerCode
    );
  }

  /**
   * Implement error boundaries
   */
  async implementErrorBoundaries() {
    const errorBoundaryCode = `
/**
 * Error Boundary System - Prevents cascading failures
 */

import { getForestLogger } from '../winston-logger.js';

const logger = getForestLogger({ module: 'ErrorBoundary' });

export class ErrorBoundary {
  constructor(name, options = {}) {
    this.name = name;
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000;
    this.fallback = options.fallback;
    this.onError = options.onError;
    this.circuitBreakerThreshold = options.circuitBreakerThreshold || 5;
    
    this.errorCount = 0;
    this.lastError = null;
    this.isCircuitOpen = false;
    this.lastCircuitCheck = Date.now();
  }

  /**
   * Execute function with error boundary protection
   */
  async execute(fn, ...args) {
    // Check circuit breaker
    if (this.isCircuitOpen && Date.now() - this.lastCircuitCheck < 60000) {
      if (this.fallback) {
        logger.warn(\`Circuit breaker open for '\${this.name}', using fallback\`);
        return await this.fallback(...args);
      }
      throw new Error(\`Circuit breaker open for '\${this.name}'\`);
    }

    let lastError;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const result = await fn(...args);
        
        // Reset error count on success
        this.errorCount = 0;
        this.isCircuitOpen = false;
        
        return result;
      } catch (error) {
        lastError = error;
        this.errorCount++;
        this.lastError = error;
        
        logger.warn(\`Error boundary '\${this.name}' caught error (attempt \${attempt}/\${this.maxRetries})\`, {
          error: error.message,
          stack: error.stack
        });

        // Check circuit breaker threshold
        if (this.errorCount >= this.circuitBreakerThreshold) {
          this.isCircuitOpen = true;
          this.lastCircuitCheck = Date.now();
          logger.error(\`Circuit breaker opened for '\${this.name}'\`);
        }

        // Call error handler
        if (this.onError) {
          await this.onError(error, attempt);
        }

        // Wait before retry (except on last attempt)
        if (attempt < this.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        }
      }
    }

    // All retries failed, try fallback
    if (this.fallback) {
      logger.warn(\`All retries failed for '\${this.name}', using fallback\`);
      return await this.fallback(...args);
    }

    throw lastError;
  }

  /**
   * Get boundary status
   */
  getStatus() {
    return {
      name: this.name,
      errorCount: this.errorCount,
      isCircuitOpen: this.isCircuitOpen,
      lastError: this.lastError?.message,
      lastCircuitCheck: this.lastCircuitCheck
    };
  }
}

// Global error boundaries registry
export const errorBoundaries = new Map();

/**
 * Create or get error boundary
 */
export function createErrorBoundary(name, options) {
  if (!errorBoundaries.has(name)) {
    errorBoundaries.set(name, new ErrorBoundary(name, options));
  }
  return errorBoundaries.get(name);
}

/**
 * Execute function with automatic error boundary
 */
export async function withErrorBoundary(name, fn, options = {}) {
  const boundary = createErrorBoundary(name, options);
  return await boundary.execute(fn);
}
`;

    await fs.writeFile(
      path.join(__dirname, 'modules/utils/error-boundary.js'),
      errorBoundaryCode
    );
  }

  /**
   * Generate comprehensive integrity report
   */
  async generateIntegrityReport(analysis) {
    const report = `
# Forest.os System Integrity Report
Generated: ${analysis.timestamp}

## Executive Summary
- Total Modules: ${analysis.dependencyAnalysis.totalModules}
- Circular Dependencies: ${analysis.circularDependencyCheck.count}
- Healthy Modules: ${analysis.moduleHealthCheck.healthyModules}/${analysis.moduleHealthCheck.totalModules}
- Brittleness Score: ${analysis.brittlenessAssessment.score}/100 (${analysis.brittlenessAssessment.level})

## Dependency Analysis
${JSON.stringify(analysis.dependencyAnalysis, null, 2)}

## Circular Dependencies
${analysis.circularDependencyCheck.detected ? 
  analysis.circularDependencyCheck.cycles.map(cycle => `- ${cycle.join(' → ')}`).join('\n') : 
  'No circular dependencies detected'}

## Module Health Issues
${analysis.moduleHealthCheck.unhealthyModules.map(module => 
  `### ${module.module} (${module.severity})\n${module.issues.map(issue => `- ${issue}`).join('\n')}`
).join('\n\n')}

## Super Glue Recommendations
${Object.entries(analysis.superGlueRecommendations).map(([category, recommendations]) =>
  `### ${category}\n${recommendations.map(rec => `- ${rec}`).join('\n')}`
).join('\n\n')}
`;

    await fs.writeFile(
      path.join(__dirname, 'logs/system-integrity-report.md'),
      report
    );

    logger.info('System integrity report generated', { 
      reportPath: 'logs/system-integrity-report.md',
      overallHealth: analysis.moduleHealthCheck.overallHealth
    });
  }

  // Helper methods
  async findJavaScriptFiles(dir) {
    const files = [];
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory() && !entry.name.startsWith('.')) {
        files.push(...await this.findJavaScriptFiles(fullPath));
      } else if (entry.isFile() && entry.name.endsWith('.js')) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  findHighestCoupledModules() {
    const coupling = new Map();
    
    for (const [module, deps] of this.dependencyGraph) {
      const totalDeps = deps.staticImports.length + deps.dynamicImports.length + deps.requires.length;
      coupling.set(module, totalDeps);
    }
    
    return Array.from(coupling.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
  }

  findIsolatedModules() {
    return Array.from(this.dependencyGraph.entries())
      .filter(([module, deps]) => 
        deps.staticImports.length === 0 && 
        deps.dynamicImports.length === 0 && 
        deps.requires.length === 0
      )
      .map(([module]) => module);
  }

  normalizeDependencyPath(dep, fromModule) {
    // Normalize relative paths to absolute module names
    if (dep.startsWith('./') || dep.startsWith('../')) {
      const fromDir = path.dirname(fromModule);
      const resolved = path.resolve(fromDir, dep);
      return path.relative(path.join(__dirname, 'modules'), resolved);
    }
    return dep;
  }

  assessCircularDependencySeverity(cycles) {
    if (cycles.length === 0) return 'none';
    if (cycles.length <= 2) return 'low';
    if (cycles.length <= 5) return 'medium';
    return 'high';
  }

  calculateOverallHealth(healthChecks) {
    const total = healthChecks.size;
    const healthy = Array.from(healthChecks.values()).filter(h => h.status === 'healthy').length;
    return Math.round((healthy / total) * 100);
  }

  calculateBrittlenessScore(factors) {
    // Calculate brittleness score based on various factors
    let score = 0;
    score += Object.keys(factors.hardcodedPaths).length * 5;
    score += factors.missingErrorHandling.length * 10;
    score += factors.synchronousOperations.length * 3;
    score += factors.singlePointsOfFailure.length * 15;
    score += factors.uncaughtExceptions.length * 8;
    score += factors.resourceLeaks.length * 7;
    
    return Math.min(score, 100);
  }

  getBrittlenessLevel(score) {
    if (score <= 20) return 'low';
    if (score <= 50) return 'medium';
    if (score <= 80) return 'high';
    return 'critical';
  }

  getHealingCapabilities() {
    return Object.fromEntries(this.healingStrategies);
  }

  // Placeholder methods for detailed analysis
  async findHardcodedPaths() { return {}; }
  async findMissingErrorHandling() { return []; }
  async findSynchronousFileOperations() { return []; }
  async identifySinglePointsOfFailure() { return []; }
  async findUncaughtExceptions() { return []; }
  async detectPotentialResourceLeaks() { return []; }
  async recommendDependencyInjection() { return []; }
  async recommendErrorBoundaries() { return []; }
  async recommendCircuitBreakers() { return []; }
  async recommendRetryMechanisms() { return []; }
  async recommendHealthChecks() { return []; }
  async recommendGracefulDegradation() { return []; }
  generateBrittlenessRecommendations(_factors) { return []; }
  async analyzeErrorTraces() { return []; }
  async createModuleFallback(_error, _context) { return null; }
  async resolveCircularDependency(_error, _context) { return null; }
  async retryAsyncInitialization(_error, _context) { return null; }
  async createProxyDependencies(_error, _context) { return null; }
  async addCircuitBreakers() {}
  async implementRetryMechanisms() {}
  async addHealthCheckEndpoints() {}
  async implementGracefulDegradation() {}
}

// Main execution
async function main() {
  const systemDebugger = new SystemIntegrityDebugger();
  
  try {
    logger.info('Starting Forest.os System Integrity Analysis');
    
    const analysis = await systemDebugger.analyzeSystemIntegrity();
    
    logger.info('Implementing super glue connections');
    await systemDebugger.implementSuperGlue();
    
    logger.info('System integrity analysis and hardening completed', {
      overallHealth: analysis.moduleHealthCheck.overallHealth,
      brittlenessLevel: analysis.brittlenessAssessment.level,
      circularDependencies: analysis.circularDependencyCheck.count
    });
    
    return analysis;
  } catch (error) {
    logger.error('System integrity analysis failed', { error: error.message, stack: error.stack });
    throw error;
  }
}

// Export for use as module
export { SystemIntegrityDebugger };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
} 