{"type": "module", "version": "1.0.0", "description": "Forest.os MCP server – production ready", "keywords": ["productivity", "learning", "task-management", "ai-assisted", "mcp-server"], "repository": {"type": "git", "url": "https://github.com/forest-os/forest-server.git"}, "bugs": {"url": "https://github.com/forest-os/forest-server/issues"}, "homepage": "https://forest-os.ai", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node server-modular.js", "dev": "set DEBUG=* && set DEBUG_HTA=true && set NODE_ENV=development && node server-modular.js", "start:prod": "NODE_ENV=production node server-modular.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:integration": "jest modules/__tests__/e2e-production-workflow.test.js", "test:edge-cases": "jest modules/__tests__/skeleton-edge-cases.test.js", "test:parsing": "jest modules/__tests__/llm-parsing-robustness.test.js", "integrity": "node test-system-integrity.js && npm run test:integration", "logs:view": "node tools/log-viewer.js", "backup:create": "node scripts/backup-data.js", "test:validation": "NODE_ENV=test node test-validation-pipeline.js", "demo:core": "NODE_ENV=demo node demo-core-loop.js", "test:isolated": "NODE_ENV=test npm run test"}, "dependencies": {"@modelcontextprotocol/sdk": "latest", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "node-fetch": "^3.3.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "jest": "^30.0.1"}}