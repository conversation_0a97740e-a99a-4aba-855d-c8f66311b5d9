#!/usr/bin/env node

/**
 * MCP Server Wrapper
 * 
 * This wrapper ensures the main() function is always called when starting the MCP server.
 * It bypasses the module detection logic that might fail in different environments.
 */

console.error('Starting Forest MCP server wrapper...');

// Import Stage1 core initialization and boot server
import('./___stage1/core-initialization.js').then(async ({ CoreInitialization }) => {
  console.error('Stage1 core-initialization imported, booting server...');
  const initializer = new CoreInitialization();
  await initializer.initialize();
  console.error('Forest MCP Stage1 server is up and running!');
}).catch(error => {
  console.error('FATAL ERROR in MCP server wrapper:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});
