/**
 * Task Strategy Core Module - Consolidated Task Intelligence & Strategy Evolution
 * Merged from task-intelligence.js + strategy-evolver.js - Preserves event-driven patterns
 */

import { bus } from './stubs/event-bus.js';
import { WebContext } from './stubs/web-context.js';
import { FILE_NAMES, DEFAULT_PATHS, TASK_CONFIG, SCORING, THRESHOLDS, EVOLUTION_STRATEGIES } from './memory-sync.js';
import { TaskScorer, TaskSelector, TaskFormatter } from './stubs/task-logic.js';

export class TaskStrategyCore {
  constructor(dataPersistence, projectManagement, llmInterface, eventBus = null) {
    this.dataPersistence = dataPersistence;
    this.projectManagement = projectManagement;
    this.webContext = new WebContext(dataPersistence, llmInterface);
    this.eventBus = eventBus || bus;
    this.logger = console;

    // Register event listeners for strategy evolution
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Listen for block completion events
    this.eventBus.on('block:completed', this.handleBlockCompletion.bind(this), 'TaskStrategyCore');
    
    // Listen for learning milestone events
    this.eventBus.on('learning:breakthrough', this.handleBreakthrough.bind(this), 'TaskStrategyCore');
    
    // Listen for opportunity detection events
    this.eventBus.on('opportunity:detected', this.handleOpportunityDetection.bind(this), 'TaskStrategyCore');
    
    // Listen for strategy evolution requests
    this.eventBus.on('strategy:evolve_requested', this.handleEvolutionRequest.bind(this), 'TaskStrategyCore');

    if (process.stdin.isTTY) {
      console.log('🧠 TaskStrategyCore event listeners registered');
    }
  }

  // ===== TASK INTELLIGENCE FUNCTIONALITY =====

  async getNextTask(contextFromMemory = '', energyLevel = 3, timeAvailable = '30 minutes') {
    try {
      const projectId = await this.projectManagement.requireActiveProject();
      const config = await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.CONFIG);

      if (!config) {
        const { ProjectConfigurationError } = await import('../errors.js');
        throw new ProjectConfigurationError(projectId, FILE_NAMES.CONFIG, null, { operation: 'getNextTask' });
      }

      // Extract project context
      const projectContext = {
        goal: config.goal,
        domain: config.domain,
        learningStyle: config.learningStyle,
        activePath: config.activePath || DEFAULT_PATHS.GENERAL
      };

      const htaData = await this.loadPathHTA(projectId, projectContext.activePath);

      // Get reasoning analysis for enhanced task selection
      let reasoningAnalysis = null;
      try {
        const { ReasoningEngine } = await import('../reasoning-engine.js');
        const reasoningEngine = new ReasoningEngine(this.dataPersistence, this.projectManagement);
        const reasoningResult = await reasoningEngine.analyzeReasoning(false);
        reasoningAnalysis = reasoningResult.reasoning_analysis;
      } catch (error) {
        console.warn('Could not get reasoning analysis for task selection:', error.message);
      }

      // Analyze context for evolution patterns
      const contextAnalysis = this.analyzeEvolutionContext(contextFromMemory);
      const hasBreakthroughContext = contextAnalysis.breakthrough;

      if (!htaData || !Array.isArray(htaData.frontierNodes) || htaData.frontierNodes.length === 0) {
        // Auto-evolve strategy for life changes or breakthroughs
        if (contextFromMemory && (TaskScorer.isLifeChangeContext(contextFromMemory) || hasBreakthroughContext)) {
          await this.evolveStrategy(hasBreakthroughContext ? `BREAKTHROUGH_CONTEXT: ${contextFromMemory}` : contextFromMemory);

          const updatedHtaData = await this.loadPathHTA(projectId, projectContext.activePath);
          if (updatedHtaData && Array.isArray(updatedHtaData.frontierNodes) && updatedHtaData.frontierNodes.length > 0) {
            const selectedTask = TaskSelector.selectOptimalTask(updatedHtaData, energyLevel, timeAvailable, contextFromMemory, projectContext, config, reasoningAnalysis);
            if (selectedTask) {
              const extSummary = await this.webContext.refreshIfNeeded(projectContext.goal, selectedTask.title || '');
              const taskResponse = TaskFormatter.formatTaskResponse(selectedTask, energyLevel, timeAvailable) +
                (extSummary ? `\n\n🌐 External context used:\n${extSummary}` : '');
              return {
                content: [{ type: 'text', text: taskResponse }],
                selected_task: selectedTask,
                energy_level: energyLevel,
                time_available: timeAvailable,
                context_used: 'yes',
                project_context: projectContext,
                auto_evolved: true
              };
            }
          }
        }

        return {
          content: [{
            type: 'text',
            text: 'ℹ️ Roadmap is in place but no actionable tasks were found. Use `generate_hta_tasks` to populate tasks from the roadmap, or run `evolve_strategy` to let the system suggest next steps.'
          }]
        };
      } else if (hasBreakthroughContext) {
        // Evolve strategy for breakthrough context with existing tasks
        await this.evolveStrategy(`BREAKTHROUGH_CONTEXT: ${contextFromMemory}`);
        const updatedHtaData = await this.loadPathHTA(projectId, projectContext.activePath);
        if (updatedHtaData) {
          const selectedTask = TaskSelector.selectOptimalTask(updatedHtaData, energyLevel, timeAvailable, contextFromMemory, projectContext, config, reasoningAnalysis);
          if (selectedTask) {
            const extSummary = await this.webContext.refreshIfNeeded(projectContext.goal, selectedTask.title || '');
            const taskResponse = TaskFormatter.formatTaskResponse(selectedTask, energyLevel, timeAvailable) +
              (extSummary ? `\n\n🌐 External context used:\n${extSummary}` : '');
            return {
              content: [{ type: 'text', text: taskResponse }],
              selected_task: selectedTask,
              energy_level: energyLevel,
              time_available: timeAvailable,
              context_used: 'yes',
              project_context: projectContext,
              breakthrough_evolved: true
            };
          }
        }
      }

      // Standard task selection
      const selectedTask = TaskSelector.selectOptimalTask(htaData, energyLevel, timeAvailable, contextFromMemory, projectContext, config, reasoningAnalysis);
      
      if (!selectedTask) {
        return {
          content: [{
            type: 'text',
            text: '🤔 No suitable tasks found for your current energy level and time availability. Try adjusting your parameters or use `evolve_strategy` to generate new tasks.'
          }]
        };
      }

      const extSummary = await this.webContext.refreshIfNeeded(projectContext.goal, selectedTask.title || '');
      const taskResponse = TaskFormatter.formatTaskResponse(selectedTask, energyLevel, timeAvailable) +
        (extSummary ? `\n\n🌐 External context used:\n${extSummary}` : '');

      return {
        content: [{ type: 'text', text: taskResponse }],
        selected_task: selectedTask,
        energy_level: energyLevel,
        time_available: timeAvailable,
        context_used: extSummary ? 'yes' : 'no',
        project_context: projectContext
      };
    } catch (error) {
      console.error('TaskStrategyCore.getNextTask failed:', error);
      return {
        content: [{
          type: 'text',
          text: `**Task Selection Failed**\n\nError: ${error.message}\n\nPlease check your project configuration and try again.`
        }],
        error: error.message
      };
    }
  }

  async loadPathHTA(projectId, pathName) {
    try {
      if (pathName === DEFAULT_PATHS.GENERAL) {
        return await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.HTA);
      } else {
        return await this.dataPersistence.loadPathData(projectId, pathName, FILE_NAMES.HTA);
      }
    } catch (error) {
      return null;
    }
  }

  analyzeEvolutionContext(contextFromMemory) {
    if (!contextFromMemory) return { breakthrough: false, lifeChange: false, opportunity: false };

    const contextLower = contextFromMemory.toLowerCase();
    
    return {
      breakthrough: contextLower.includes('breakthrough') || contextLower.includes('major insight') || contextLower.includes('significant progress'),
      lifeChange: contextLower.includes('life change') || contextLower.includes('new job') || contextLower.includes('career change'),
      opportunity: contextLower.includes('opportunity') || contextLower.includes('chance') || contextLower.includes('opening')
    };
  }

  // ===== STRATEGY EVOLUTION FUNCTIONALITY =====

  async handleBlockCompletion({ projectId, pathName, block, _eventMetadata }) {
    try {
      console.log(`🔄 TaskStrategyCore processing block completion: ${block.title}`);

      // Only evolve if there's actual learning to process
      if (!block.learned && !block.nextQuestions && !block.breakthrough) {
        console.log('📝 No learning content to process, skipping HTA evolution');
        return;
      }

      await this.evolveHTABasedOnLearning(projectId, pathName, block);

      // Emit follow-up events based on the learning content
      if (block.breakthrough) {
        this.eventBus.emit('learning:breakthrough', {
          projectId,
          pathName,
          block,
          breakthroughContent: block.learned,
        }, 'TaskStrategyCore');
      }

      if (block.opportunityContext) {
        this.eventBus.emit('opportunity:detected', {
          projectId,
          pathName,
          block,
          opportunities: block.opportunityContext,
        }, 'TaskStrategyCore');
      }
    } catch (error) {
      console.error('❌ TaskStrategyCore failed to handle block completion:', error.message);
    }
  }

  async handleBreakthrough({ projectId, pathName, block, breakthroughContent }) {
    try {
      console.log(`🚀 Processing breakthrough for project ${projectId}`);
      
      // Generate follow-up tasks based on breakthrough
      const followUpTasks = await this.generateFollowUpTasks(projectId, pathName, breakthroughContent, 'breakthrough');
      
      if (followUpTasks && followUpTasks.length > 0) {
        await this.addTasksToHTA(projectId, pathName, followUpTasks);
        console.log(`✅ Added ${followUpTasks.length} breakthrough follow-up tasks`);
      }
    } catch (error) {
      console.error('❌ Failed to handle breakthrough:', error.message);
    }
  }

  async handleOpportunityDetection({ projectId, pathName, block, opportunities }) {
    try {
      console.log(`🎯 Processing opportunity detection for project ${projectId}`);
      
      // Generate opportunity-specific tasks
      const opportunityTasks = await this.generateFollowUpTasks(projectId, pathName, opportunities, 'opportunity');
      
      if (opportunityTasks && opportunityTasks.length > 0) {
        await this.addTasksToHTA(projectId, pathName, opportunityTasks);
        console.log(`✅ Added ${opportunityTasks.length} opportunity-based tasks`);
      }
    } catch (error) {
      console.error('❌ Failed to handle opportunity detection:', error.message);
    }
  }

  async handleEvolutionRequest({ projectId, pathName, feedback, _eventMetadata }) {
    try {
      console.log(`🧬 Processing evolution request for project ${projectId}`);
      await this.evolveStrategy(feedback, projectId, pathName);
    } catch (error) {
      console.error('❌ Failed to handle evolution request:', error.message);
    }
  }

  async evolveStrategy(feedback = '', projectId = null, pathName = null) {
    try {
      const activeProjectId = projectId || await this.projectManagement.requireActiveProject();
      const config = await this.dataPersistence.loadProjectData(activeProjectId, FILE_NAMES.CONFIG);
      const activePath = pathName || config?.activePath || DEFAULT_PATHS.GENERAL;

      console.log(`🧬 Evolving strategy for project ${activeProjectId}, path: ${activePath}`);

      // Load current HTA data
      const htaData = await this.loadPathHTA(activeProjectId, activePath);
      if (!htaData) {
        throw new Error('No HTA data found to evolve');
      }

      // Analyze feedback and current progress
      const evolutionContext = this.analyzeEvolutionNeeds(feedback, htaData);
      
      // Generate new tasks based on evolution context
      const newTasks = await this.generateEvolutionTasks(activeProjectId, activePath, evolutionContext, config);
      
      if (newTasks && newTasks.length > 0) {
        await this.addTasksToHTA(activeProjectId, activePath, newTasks);
        console.log(`✅ Strategy evolved: Added ${newTasks.length} new tasks`);
        
        return {
          success: true,
          content: [{
            type: 'text',
            text: `**Strategy Evolution Complete**\n\n🧬 **Analysis**: ${evolutionContext.summary}\n\n📋 **New Tasks Added**: ${newTasks.length}\n\n🎯 **Focus**: ${evolutionContext.focus}\n\nUse \`get_next_task\` to continue with your evolved strategy!`
          }],
          tasks_added: newTasks.length,
          evolution_context: evolutionContext
        };
      } else {
        return {
          success: true,
          content: [{
            type: 'text',
            text: `**Strategy Analysis Complete**\n\n🧬 **Analysis**: ${evolutionContext.summary}\n\n📋 **Current tasks are well-aligned with your progress. Continue with \`get_next_task\`!**`
          }],
          tasks_added: 0,
          evolution_context: evolutionContext
        };
      }
    } catch (error) {
      console.error('TaskStrategyCore.evolveStrategy failed:', error);
      return {
        success: false,
        content: [{
          type: 'text',
          text: `**Strategy Evolution Failed**\n\nError: ${error.message}\n\nPlease try again or check your project configuration.`
        }],
        error: error.message
      };
    }
  }

  // ===== CORE EVOLUTION METHODS =====

  async evolveHTABasedOnLearning(projectId, pathName, block) {
    try {
      const htaData = await this.loadPathHTA(projectId, pathName);
      if (!htaData) {
        console.warn('No HTA data found for evolution');
        return;
      }

      // Analyze learning content for evolution opportunities
      const learningAnalysis = this.analyzeLearningContent(block);

      if (learningAnalysis.shouldEvolve) {
        const newTasks = await this.generateFollowUpTasks(projectId, pathName, block.learned, learningAnalysis.evolutionType);

        if (newTasks && newTasks.length > 0) {
          await this.addTasksToHTA(projectId, pathName, newTasks);
          console.log(`✅ HTA evolved: Added ${newTasks.length} tasks based on learning`);
        }
      }
    } catch (error) {
      console.error('Failed to evolve HTA based on learning:', error.message);
    }
  }

  analyzeLearningContent(block) {
    const learned = block.learned || '';
    const questions = block.nextQuestions || '';
    const breakthrough = block.breakthrough || false;

    let evolutionType = 'standard';
    let shouldEvolve = false;

    if (breakthrough) {
      evolutionType = 'breakthrough';
      shouldEvolve = true;
    } else if (questions.length > 50) { // Substantial questions indicate deep engagement
      evolutionType = 'deep_dive';
      shouldEvolve = true;
    } else if (learned.length > 100) { // Substantial learning content
      evolutionType = 'progressive';
      shouldEvolve = true;
    }

    return {
      shouldEvolve,
      evolutionType,
      learningDepth: learned.length,
      questionDepth: questions.length,
      hasBreakthrough: breakthrough
    };
  }

  analyzeEvolutionNeeds(feedback, htaData) {
    const feedbackLower = feedback.toLowerCase();
    let focus = 'general';
    let summary = 'Analyzing current progress and identifying growth opportunities';

    if (feedbackLower.includes('breakthrough') || feedbackLower.includes('major insight')) {
      focus = 'breakthrough_expansion';
      summary = 'Breakthrough detected - expanding learning path to capitalize on new insights';
    } else if (feedbackLower.includes('stuck') || feedbackLower.includes('difficult')) {
      focus = 'difficulty_support';
      summary = 'Challenges identified - generating supportive tasks and alternative approaches';
    } else if (feedbackLower.includes('fast') || feedbackLower.includes('easy')) {
      focus = 'acceleration';
      summary = 'Rapid progress detected - escalating complexity and introducing advanced concepts';
    } else if (feedbackLower.includes('interest') || feedbackLower.includes('curious')) {
      focus = 'interest_expansion';
      summary = 'New interests identified - expanding scope to explore related areas';
    }

    const completedTasks = htaData.frontierNodes?.filter(task => task.completed) || [];
    const completionRate = htaData.frontierNodes?.length > 0 ? completedTasks.length / htaData.frontierNodes.length : 0;

    if (completionRate > 0.7) {
      focus = 'advanced_progression';
      summary = 'High completion rate - generating advanced tasks for continued growth';
    }

    return {
      focus,
      summary,
      completionRate,
      feedback: feedback,
      needsNewTasks: completionRate > 0.5 || focus !== 'general'
    };
  }

  async generateEvolutionTasks(projectId, pathName, evolutionContext, config) {
    const tasks = [];
    const { focus, completionRate } = evolutionContext;

    // Generate tasks based on evolution focus
    switch (focus) {
      case 'breakthrough_expansion':
        tasks.push(...this.generateBreakthroughTasks(config, pathName));
        break;
      case 'difficulty_support':
        tasks.push(...this.generateSupportTasks(config, pathName));
        break;
      case 'acceleration':
        tasks.push(...this.generateAdvancedTasks(config, pathName));
        break;
      case 'interest_expansion':
        tasks.push(...this.generateExplorationTasks(config, pathName));
        break;
      case 'advanced_progression':
        tasks.push(...this.generateProgressionTasks(config, pathName));
        break;
      default:
        if (completionRate > 0.5) {
          tasks.push(...this.generateStandardEvolutionTasks(config, pathName));
        }
    }

    return tasks;
  }

  async generateFollowUpTasks(projectId, pathName, content, type) {
    const config = await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.CONFIG);
    if (!config) return [];

    const tasks = [];
    const baseId = Date.now();

    switch (type) {
      case 'breakthrough':
        tasks.push({
          id: `breakthrough_${baseId}`,
          title: `Capitalize on Recent Breakthrough`,
          description: `Build upon your recent breakthrough: ${content.substring(0, 100)}...`,
          difficulty: 4,
          duration: '45 minutes',
          branch: 'Breakthrough Expansion',
          priority: 50, // High priority
          prerequisites: [],
          learningOutcome: 'Maximize breakthrough potential',
          generated: true,
          evolutionGenerated: true
        });
        break;
      case 'opportunity':
        tasks.push({
          id: `opportunity_${baseId}`,
          title: `Explore New Opportunity`,
          description: `Investigate opportunity: ${content.substring(0, 100)}...`,
          difficulty: 3,
          duration: '30 minutes',
          branch: 'Opportunity Exploration',
          priority: 75,
          prerequisites: [],
          learningOutcome: 'Opportunity assessment and planning',
          generated: true,
          evolutionGenerated: true
        });
        break;
      default:
        tasks.push({
          id: `followup_${baseId}`,
          title: `Follow Up on Learning`,
          description: `Continue exploring: ${content.substring(0, 100)}...`,
          difficulty: 2,
          duration: '25 minutes',
          branch: 'Learning Continuation',
          priority: 100,
          prerequisites: [],
          learningOutcome: 'Deepen understanding',
          generated: true,
          evolutionGenerated: true
        });
    }

    return tasks;
  }

  generateBreakthroughTasks(config, pathName) {
    const baseId = Date.now();
    return [{
      id: `breakthrough_advanced_${baseId}`,
      title: `Advanced ${config.goal} Concepts`,
      description: `Explore advanced concepts building on your breakthrough insights`,
      difficulty: 5,
      duration: '60 minutes',
      branch: 'Advanced Mastery',
      priority: 25,
      prerequisites: [],
      learningOutcome: 'Master advanced concepts',
      generated: true,
      evolutionGenerated: true
    }];
  }

  generateSupportTasks(config, pathName) {
    const baseId = Date.now();
    return [{
      id: `support_${baseId}`,
      title: `Alternative Approach to ${config.goal}`,
      description: `Try a different approach to overcome current challenges`,
      difficulty: 2,
      duration: '30 minutes',
      branch: 'Alternative Methods',
      priority: 60,
      prerequisites: [],
      learningOutcome: 'Find effective learning strategies',
      generated: true,
      evolutionGenerated: true
    }];
  }

  generateAdvancedTasks(config, pathName) {
    const baseId = Date.now();
    return [{
      id: `advanced_${baseId}`,
      title: `Accelerated ${config.goal} Challenge`,
      description: `Take on an advanced challenge to match your rapid progress`,
      difficulty: 4,
      duration: '45 minutes',
      branch: 'Accelerated Learning',
      priority: 40,
      prerequisites: [],
      learningOutcome: 'Push learning boundaries',
      generated: true,
      evolutionGenerated: true
    }];
  }

  generateExplorationTasks(config, pathName) {
    const baseId = Date.now();
    return [{
      id: `explore_${baseId}`,
      title: `Explore Related Areas of ${config.goal}`,
      description: `Investigate interesting connections and related topics`,
      difficulty: 3,
      duration: '35 minutes',
      branch: 'Interest Exploration',
      priority: 80,
      prerequisites: [],
      learningOutcome: 'Broaden knowledge base',
      generated: true,
      evolutionGenerated: true
    }];
  }

  generateProgressionTasks(config, pathName) {
    const baseId = Date.now();
    return [{
      id: `progression_${baseId}`,
      title: `Next Level ${config.goal} Skills`,
      description: `Progress to the next level of expertise`,
      difficulty: 4,
      duration: '50 minutes',
      branch: 'Skill Progression',
      priority: 30,
      prerequisites: [],
      learningOutcome: 'Advance skill level',
      generated: true,
      evolutionGenerated: true
    }];
  }

  generateStandardEvolutionTasks(config, pathName) {
    const baseId = Date.now();
    return [{
      id: `evolution_${baseId}`,
      title: `Continue ${config.goal} Journey`,
      description: `Build upon your progress with the next logical steps`,
      difficulty: 3,
      duration: '30 minutes',
      branch: 'Continued Growth',
      priority: 90,
      prerequisites: [],
      learningOutcome: 'Maintain learning momentum',
      generated: true,
      evolutionGenerated: true
    }];
  }

  async addTasksToHTA(projectId, pathName, newTasks) {
    try {
      const htaData = await this.loadPathHTA(projectId, pathName);
      if (!htaData) {
        throw new Error('No HTA data found to update');
      }

      // Add new tasks to frontier nodes
      htaData.frontierNodes = htaData.frontierNodes || [];
      htaData.frontierNodes.push(...newTasks);

      // Update metadata
      htaData.hierarchyMetadata = htaData.hierarchyMetadata || {};
      htaData.hierarchyMetadata.total_tasks = htaData.frontierNodes.length;
      htaData.hierarchyMetadata.last_updated = new Date().toISOString();
      htaData.lastUpdated = new Date().toISOString();

      // Save updated HTA data
      if (pathName === DEFAULT_PATHS.GENERAL) {
        await this.dataPersistence.saveProjectData(projectId, FILE_NAMES.HTA, htaData);
      } else {
        await this.dataPersistence.savePathData(projectId, pathName, FILE_NAMES.HTA, htaData);
      }

      console.log(`✅ Added ${newTasks.length} tasks to HTA for path: ${pathName}`);
    } catch (error) {
      console.error('Failed to add tasks to HTA:', error.message);
      throw error;
    }
  }
}
