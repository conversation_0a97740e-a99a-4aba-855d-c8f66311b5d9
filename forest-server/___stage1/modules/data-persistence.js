/**
 * Data Persistence Module - Consolidated Data Management
 * Optimized from data-persistence.js - Preserves atomic operations, caching, and validation
 */

import { promises as fs } from 'fs';
import path from 'path';
import { FILE_NAMES, DEFAULT_PATHS } from './memory-sync.js';
import { CacheManager } from './stubs/cache-manager.js';
// Use simple console logger for Stage1 to avoid complex initialization
const loggerModule = {
  getLogger: async () => ({
    debug: (...args) => console.log('[DEBUG]', ...args),
    info: (...args) => console.log('[INFO]', ...args),
    warn: (...args) => console.warn('[WARN]', ...args),
    error: (...args) => console.error('[ERROR]', ...args)
  })
};

export class DataPersistence {
  constructor(dataDir = './.forest-data') {
    this.dataDir = dataDir;
    this.cache = new CacheManager();
    this.transactions = new Map(); // Active transactions
    this.logger = null; // Will be initialized lazily
  }

  async getLogger() {
    if (!this.logger) {
      this.logger = await loggerModule.getLogger();
    }
    return this.logger;
  }

  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw new Error(`Failed to create data directory: ${error.message}`);
      }
    }
  }

  // ===== PROJECT DATA OPERATIONS =====

  async saveProjectData(projectId, fileName, data, transaction = null) {
    try {
      await this.ensureDataDir();
      const projectDir = path.join(this.dataDir, projectId);
      await fs.mkdir(projectDir, { recursive: true });
      
      const filePath = path.join(projectDir, fileName);

      // Normalize data before saving
      let normalizedData = data;
      if (fileName === FILE_NAMES.HTA && data && typeof data === 'object') {
        normalizedData = this._normalizeHTAData(data);
      }

      // Atomic write with validation
      await this._atomicWriteJSON(filePath, normalizedData);

      // Only invalidate cache AFTER successful write
      this.invalidateProjectCache(projectId);
      
      this.logger.debug('[DataPersistence] Project data saved', {
        projectId,
        fileName,
        hasTransaction: !!transaction,
        dataSize: JSON.stringify(normalizedData).length
      });

      return true;
    } catch (error) {
      this.logger.error('[DataPersistence] Failed to save project data', {
        projectId,
        fileName,
        error: error.message
      });
      throw error;
    }
  }

  async loadProjectData(projectId, fileName) {
    try {
      const cacheKey = `project:${projectId}:${fileName}`;
      const cached = this.cache.get(cacheKey);
      if (cached) {
        this.logger.debug('[DataPersistence] Cache hit for project data', { projectId, fileName });
        return cached;
      }

      const filePath = path.join(this.dataDir, projectId, fileName);
      const data = await this._readJSON(filePath);
      
      if (data) {
        this.cache.set(cacheKey, data);
        this.logger.debug('[DataPersistence] Project data loaded', { projectId, fileName });
      }
      
      return data;
    } catch (error) {
      if (error.code === 'ENOENT') {
        this.logger.debug('[DataPersistence] Project data file not found', { projectId, fileName });
        return null;
      }
      this.logger.error('[DataPersistence] Failed to load project data', {
        projectId,
        fileName,
        error: error.message
      });
      throw error;
    }
  }

  // ===== PATH DATA OPERATIONS =====

  async savePathData(projectId, pathName, fileName, data, transaction = null) {
    try {
      await this.ensureDataDir();
      const pathDir = path.join(this.dataDir, projectId, 'paths', pathName);
      await fs.mkdir(pathDir, { recursive: true });
      
      const filePath = path.join(pathDir, fileName);

      // Normalize data before saving
      let normalizedData = data;
      if (fileName === FILE_NAMES.HTA && data && typeof data === 'object') {
        normalizedData = this._normalizeHTAData(data);
      }

      // Atomic write with validation
      await this._atomicWriteJSON(filePath, normalizedData);

      // Only invalidate cache AFTER successful write
      this.invalidateProjectCache(projectId);
      
      this.logger.debug('[DataPersistence] Path data saved', {
        projectId,
        pathName,
        fileName,
        hasTransaction: !!transaction,
        dataSize: JSON.stringify(normalizedData).length
      });

      return true;
    } catch (error) {
      this.logger.error('[DataPersistence] Failed to save path data', {
        projectId,
        pathName,
        fileName,
        error: error.message
      });
      throw error;
    }
  }

  async loadPathData(projectId, pathName, fileName) {
    try {
      const cacheKey = `path:${projectId}:${pathName}:${fileName}`;
      const cached = this.cache.get(cacheKey);
      if (cached) {
        this.logger.debug('[DataPersistence] Cache hit for path data', { projectId, pathName, fileName });
        return cached;
      }

      const filePath = path.join(this.dataDir, projectId, 'paths', pathName, fileName);
      const data = await this._readJSON(filePath);
      
      if (data) {
        this.cache.set(cacheKey, data);
        this.logger.debug('[DataPersistence] Path data loaded', { projectId, pathName, fileName });
      }
      
      return data;
    } catch (error) {
      if (error.code === 'ENOENT') {
        this.logger.debug('[DataPersistence] Path data file not found', { projectId, pathName, fileName });
        return null;
      }
      this.logger.error('[DataPersistence] Failed to load path data', {
        projectId,
        pathName,
        fileName,
        error: error.message
      });
      throw error;
    }
  }

  // ===== GLOBAL DATA OPERATIONS =====

  async saveGlobalData(fileName, data) {
    try {
      await this.ensureDataDir();
      const filePath = path.join(this.dataDir, fileName);
      
      await this._atomicWriteJSON(filePath, data);
      this.cache.delete(`global:${fileName}`);
      
      this.logger.debug('[DataPersistence] Global data saved', { fileName });
      return true;
    } catch (error) {
      this.logger.error('[DataPersistence] Failed to save global data', {
        fileName,
        error: error.message
      });
      throw error;
    }
  }

  async loadGlobalData(fileName) {
    try {
      const cacheKey = `global:${fileName}`;
      const cached = this.cache.get(cacheKey);
      if (cached) {
        this.logger.debug('[DataPersistence] Cache hit for global data', { fileName });
        return cached;
      }

      const filePath = path.join(this.dataDir, fileName);
      const data = await this._readJSON(filePath);
      
      if (data) {
        this.cache.set(cacheKey, data);
        this.logger.debug('[DataPersistence] Global data loaded', { fileName });
      }
      
      return data;
    } catch (error) {
      if (error.code === 'ENOENT') {
        this.logger.debug('[DataPersistence] Global data file not found', { fileName });
        return null;
      }
      this.logger.error('[DataPersistence] Failed to load global data', {
        fileName,
        error: error.message
      });
      throw error;
    }
  }

  // ===== TRANSACTION SUPPORT =====

  beginTransaction() {
    const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.transactions.set(transactionId, {
      id: transactionId,
      operations: [],
      startTime: Date.now()
    });
    
    this.logger.debug('[DataPersistence] Transaction started', { transactionId });
    return transactionId;
  }

  async commitTransaction(transactionId) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      throw new Error(`Transaction ${transactionId} not found`);
    }

    try {
      // All operations were already executed during the transaction
      // This is just cleanup
      this.transactions.delete(transactionId);
      
      this.logger.debug('[DataPersistence] Transaction committed', {
        transactionId,
        operationCount: transaction.operations.length,
        duration: Date.now() - transaction.startTime
      });
      
      return true;
    } catch (error) {
      this.logger.error('[DataPersistence] Transaction commit failed', {
        transactionId,
        error: error.message
      });
      throw error;
    }
  }

  async rollbackTransaction(transactionId) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      throw new Error(`Transaction ${transactionId} not found`);
    }

    try {
      // For simplicity, we'll just clear the transaction
      // In a full implementation, we'd restore previous states
      this.transactions.delete(transactionId);
      
      this.logger.debug('[DataPersistence] Transaction rolled back', {
        transactionId,
        operationCount: transaction.operations.length
      });
      
      return true;
    } catch (error) {
      this.logger.error('[DataPersistence] Transaction rollback failed', {
        transactionId,
        error: error.message
      });
      throw error;
    }
  }

  // ===== CACHE MANAGEMENT =====

  invalidateProjectCache(projectId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.startsWith(`project:${projectId}:`) || key.startsWith(`path:${projectId}:`)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      this.logger.debug('[DataPersistence] Cache invalidated', {
        projectId,
        keysInvalidated: keysToDelete.length
      });
    }
  }

  clearCache() {
    this.cache.clear();
    this.logger.debug('[DataPersistence] Cache cleared');
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      hitRate: this.cache.getHitRate(),
      memoryUsage: this.cache.getMemoryUsage()
    };
  }

  // ===== UTILITY METHODS =====

  async _atomicWriteJSON(filePath, data) {
    const tempPath = `${filePath}.tmp`;

    try {
      // Write to temporary file first
      await fs.writeFile(tempPath, JSON.stringify(data, null, 2), 'utf8');

      // Atomic move to final location
      await fs.rename(tempPath, filePath);

      return true;
    } catch (error) {
      // Clean up temp file if it exists
      try {
        await fs.unlink(tempPath);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      throw error;
    }
  }

  async _readJSON(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return null;
      }
      throw error;
    }
  }

  _normalizeHTAData(data) {
    if (!data || typeof data !== 'object') return data;

    const normalized = { ...data };

    // Ensure frontierNodes is an array
    if (!Array.isArray(normalized.frontierNodes)) {
      normalized.frontierNodes = [];
    }

    // Normalize each frontier node
    normalized.frontierNodes = normalized.frontierNodes.map(node => {
      if (!node || typeof node !== 'object') return node;

      return {
        ...node,
        id: node.id || `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        difficulty: typeof node.difficulty === 'number' ? Math.max(1, Math.min(10, node.difficulty)) : 3,
        priority: typeof node.priority === 'number' ? Math.max(1, Math.min(100, node.priority)) : 50,
        prerequisites: Array.isArray(node.prerequisites) ? node.prerequisites : [],
        completed: Boolean(node.completed),
        generated: Boolean(node.generated)
      };
    });

    // Ensure hierarchyMetadata exists
    if (!normalized.hierarchyMetadata || typeof normalized.hierarchyMetadata !== 'object') {
      normalized.hierarchyMetadata = {};
    }

    // Update metadata
    normalized.hierarchyMetadata.total_tasks = normalized.frontierNodes.length;
    normalized.hierarchyMetadata.last_updated = new Date().toISOString();
    normalized.lastUpdated = new Date().toISOString();

    return normalized;
  }

  async logError(operation, error, context = {}) {
    const errorLog = {
      timestamp: new Date().toISOString(),
      operation,
      error: {
        message: error.message,
        stack: error.stack?.substring(0, 1000), // Limit stack trace
        name: error.name
      },
      context
    };

    try {
      await this.ensureDataDir();
      const errorLogPath = path.join(this.dataDir, 'error.log');
      const logEntry = JSON.stringify(errorLog) + '\n';

      await fs.appendFile(errorLogPath, logEntry, 'utf8');

      this.logger.error('[DataPersistence] Error logged', {
        operation,
        error: error.message
      });
    } catch (logError) {
      // If we can't log to file, at least log to console
      console.error('Failed to write error log:', logError.message);
      console.error('Original error:', error.message);
    }
  }

  async getProjectList() {
    try {
      await this.ensureDataDir();
      const entries = await fs.readdir(this.dataDir, { withFileTypes: true });

      const projects = [];
      for (const entry of entries) {
        if (entry.isDirectory() && !entry.name.startsWith('.')) {
          try {
            const configPath = path.join(this.dataDir, entry.name, FILE_NAMES.CONFIG);
            const config = await this._readJSON(configPath);
            if (config) {
              projects.push({
                id: entry.name,
                goal: config.goal,
                created_at: config.created_at,
                progress: config.progress || 0
              });
            }
          } catch (error) {
            // Skip projects without valid config
            this.logger.debug('[DataPersistence] Skipping invalid project', {
              projectId: entry.name,
              error: error.message
            });
          }
        }
      }

      return projects;
    } catch (error) {
      this.logger.error('[DataPersistence] Failed to get project list', {
        error: error.message
      });
      return [];
    }
  }

  async projectExists(projectId) {
    try {
      const projectDir = path.join(this.dataDir, projectId);
      const stats = await fs.stat(projectDir);
      return stats.isDirectory();
    } catch (error) {
      return false;
    }
  }

  async deleteProject(projectId) {
    try {
      const projectDir = path.join(this.dataDir, projectId);
      await fs.rm(projectDir, { recursive: true, force: true });

      // Clear related cache entries
      this.invalidateProjectCache(projectId);

      this.logger.info('[DataPersistence] Project deleted', { projectId });
      return true;
    } catch (error) {
      this.logger.error('[DataPersistence] Failed to delete project', {
        projectId,
        error: error.message
      });
      throw error;
    }
  }
}
