/**
 * HTA Core Module - Consolidated HTA Intelligence
 * Merged from hta-tree-builder.js + hta-bridge.js - Preserves ALL HTA magic
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { FILE_NAMES, DEFAULT_PATHS, HTA_LEVELS, FEATURE_FLAGS } from './memory-sync.js';
import { buildRichContext, formatConstraintsForPrompt } from '../../modules/context-utils.js';
import { globalCircuitBreaker } from '../../modules/utils/llm-circuit-breaker.js';

// PERMANENT_SCHEMA_FIX_INSTALLED: 2025-06-29T03:20:13.423Z
const PERMANENT_SCHEMA_FIX = {
  version: '1.0.0',
  installed: '2025-06-29T03:20:13.426Z',
  description: 'Handles resultSchema.parse errors gracefully'
};

if (!PERMANENT_SCHEMA_FIX.version) {
  throw new Error('CRITICAL: Permanent schema fix has been corrupted or removed');
}

export class HTACore {
  constructor(dataPersistence, projectManagement, claudeInterface) {
    this.dataPersistence = dataPersistence;
    this.projectManagement = projectManagement;
    this.claudeInterface = claudeInterface;
    this.htaClient = null;
    this.htaTransport = null;
    this.isConnected = false;
    this.logger = { info: () => {}, warn: () => {}, error: () => {}, debug: () => {} };

    // Dependency validation
    if (!this.projectManagement || typeof this.projectManagement.requireActiveProject !== 'function') {
      this.logger.warn('[HTACore] ⚠️ projectManagement missing requireActiveProject');
    }
  }

  // ===== HTA BRIDGE FUNCTIONALITY =====

  async connect() {
    if (this.isConnected) return;
    try {
      const path = await import('path');
      const { fileURLToPath } = await import('url');
      const __dirname = path.dirname(fileURLToPath(import.meta.url));
      const htaServerPath = path.resolve(__dirname, '../../../hta-analysis-server.js');

      this.htaTransport = new StdioClientTransport({
        command: 'node',
        args: [htaServerPath]
      });

      this.htaClient = new Client(
        { name: 'forest-hta-bridge', version: '1.0.0' },
        { capabilities: {} }
      );

      await this.htaClient.connect(this.htaTransport);
      this.isConnected = true;
      console.log('✅ Connected to HTA Analysis Server');
    } catch (error) {
      console.warn('⚠️ Could not connect to HTA Analysis Server:', error.message);
      this.isConnected = false;
    }
  }

  async disconnect() {
    if (this.htaTransport) {
      await this.htaTransport.close();
      this.isConnected = false;
    }
  }

  async getOrCreateHTAData(projectId, pathName) {
    let existingHTA = null;
    try {
      if (pathName === 'general') {
        existingHTA = await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.HTA);
      } else {
        existingHTA = await this.dataPersistence.loadPathData(projectId, pathName, FILE_NAMES.HTA);
      }
    } catch (error) {
      console.warn('Could not load existing HTA data:', error.message);
    }

    if (existingHTA && existingHTA.frontierNodes && existingHTA.frontierNodes.length > 0) {
      console.log(`📁 Using existing HTA data for path: ${pathName}`);
      return existingHTA;
    }

    const config = await this.dataPersistence.loadProjectData(projectId, 'config.json');
    if (!config) {
      throw new Error(`Project configuration not found for project: ${projectId}`);
    }

    await this.connect();
    if (this.isConnected) {
      try {
        const htaResponse = await this.htaClient.request({
          method: 'generate_hta_structure',
          params: {
            goal: config.goal,
            context: config.context || '',
            learning_style: config.learningStyle || 'mixed',
            focus_areas: config.focusAreas || [],
            path_name: pathName
          }
        });

        const htaStructure = htaResponse.hta_structure || htaResponse.result?.hta_structure;
        if (!htaStructure) {
          console.warn('⚠️ No HTA structure in response, using fallback');
          return this.createFallbackHTA(config, pathName);
        }

        const convertedHTA = this.convertHTAStructureToTaskFormat(htaStructure, pathName);
        await this.saveHTAData(projectId, pathName, convertedHTA);
        console.log(`✅ Generated and saved HTA data for path: ${pathName}`);
        return convertedHTA;
      } catch (error) {
        console.warn('⚠️ HTA Analysis Server request failed:', error.message);
        return this.createFallbackHTA(config, pathName);
      }
    } else {
      return this.createFallbackHTA(config, pathName);
    }
  }

  convertHTAStructureToTaskFormat(htaStructure, pathName) {
    let frontierNodes = [];
    if (htaStructure.strategic_branches && htaStructure.strategic_branches.length > 0) {
      frontierNodes = this.generateTasksFromBranches(htaStructure.strategic_branches, htaStructure.goal);
    }

    const hierarchyMetadata = {
      total_tasks: frontierNodes.length,
      total_branches: htaStructure.strategic_branches?.length || 0,
      completed_tasks: 0,
      available_tasks: frontierNodes.length,
      depth_levels: htaStructure.depth_config?.target_depth || 3,
      last_updated: new Date().toISOString()
    };

    return {
      pathName: pathName,
      goal: htaStructure.goal,
      complexity: htaStructure.complexity_profile?.complexity_score || 5,
      targetDepth: htaStructure.depth_config?.target_depth || 3,
      strategicBranches: htaStructure.strategic_branches || [],
      questionTree: htaStructure.question_tree || { root_question: `How to achieve: ${htaStructure.goal}?`, sub_questions: [] },
      dependencies: htaStructure.dependencies || {},
      metadata: htaStructure.metadata || { source: 'hta-server' },
      frontierNodes: frontierNodes,
      hierarchyMetadata: hierarchyMetadata,
      created: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };
  }

  generateTasksFromBranches(strategicBranches, goal) {
    const tasks = [];
    let taskId = 1;

    strategicBranches.forEach((branch, branchIndex) => {
      if (branch.tasks && Array.isArray(branch.tasks)) {
        branch.tasks.forEach((task, taskIndex) => {
          tasks.push({
            id: `${branch.name.toLowerCase().replace(/\s+/g, '_')}_${taskId}`,
            title: task.title || task.name || `${branch.name} Task ${taskIndex + 1}`,
            description: task.description || `Work on ${branch.name} objectives`,
            difficulty: task.difficulty || 2,
            duration: task.duration || '25 minutes',
            branch: branch.name,
            priority: (branchIndex + 1) * 100 + taskIndex,
            prerequisites: task.prerequisites || [],
            learningOutcome: task.learning_outcome || `Progress in ${branch.name}`,
            generated: true
          });
          taskId++;
        });
      } else {
        tasks.push({
          id: `${branch.name.toLowerCase().replace(/\s+/g, '_')}_${taskId}`,
          title: `Explore ${branch.name}`,
          description: branch.description || `Work on ${branch.name} objectives`,
          difficulty: 2,
          duration: '25 minutes',
          branch: branch.name,
          priority: (branchIndex + 1) * 100,
          prerequisites: [],
          learningOutcome: `Understanding of ${branch.name}`,
          generated: true
        });
        taskId++;
      }
    });

    return tasks;
  }

  async saveHTAData(projectId, pathName, htaData) {
    try {
      if (pathName === 'general') {
        await this.dataPersistence.saveProjectData(projectId, FILE_NAMES.HTA, htaData);
      } else {
        await this.dataPersistence.savePathData(projectId, pathName, FILE_NAMES.HTA, htaData);
      }
    } catch (error) {
      console.error('Failed to save HTA data:', error);
      throw error;
    }
  }

  createFallbackHTA(config, pathName) {
    const complexityAnalysis = this.analyzeGoalComplexity(config.goal, config.context || '');
    const skeletonTasks = this.generateSkeletonTasks(complexityAnalysis, config, [], config.learningStyle || 'mixed');
    
    return {
      pathName: pathName,
      goal: config.goal,
      complexity: complexityAnalysis,
      strategicBranches: this.deriveStrategicBranches(skeletonTasks),
      frontierNodes: skeletonTasks,
      hierarchyMetadata: {
        total_tasks: skeletonTasks.length,
        total_branches: 0,
        completed_tasks: 0,
        available_tasks: skeletonTasks.length,
        depth_levels: complexityAnalysis.recommended_depth,
        last_updated: new Date().toISOString()
      },
      created: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      metadata: { source: 'fallback-generation' }
    };
  }

  // ===== HTA TREE BUILDER FUNCTIONALITY =====

  async buildHTATree(pathName, learningStyle = 'mixed', focusAreas = [], goalOverride = null, contextOverride = '') {
    try {
      if (!this.projectManagement) {
        throw new Error('ProjectManagement instance is null or undefined in HTACore');
      }

      const projectId = await this.projectManagement.requireActiveProject();
      if (pathName && pathName !== 'general') {
        await this.projectManagement.updateActivePath(pathName);
      }

      const existingHTA = await this.loadPathHTA(projectId, pathName || 'general');
      if (existingHTA && existingHTA.frontierNodes && existingHTA.frontierNodes.length > 0) {
        return {
          success: true,
          content: [{
            type: 'text',
            text: `**HTA Tree Already Exists**\n\n**Goal**: ${existingHTA.goal}\n**Complexity**: ${existingHTA.complexity?.score || 'Unknown'}/10\n**Tasks**: ${existingHTA.frontierNodes.length} generated\n**Created**: ${existingHTA.created}\n\n**Tree is ready!** Use \`get_next_task\` to continue your journey.\n\n**Note**: HTA trees are generated only once per project. Use branch evolution to expand specific areas as you learn.`
          }],
          existing_tree: true,
          tasks_count: existingHTA.frontierNodes.length,
          complexity: existingHTA.complexity,
          next_task: await this.getNextTaskFromExistingTree(existingHTA)
        };
      }

      const config = await this.dataPersistence.loadProjectData(projectId, 'config.json');
      if (!config) {
        throw new Error(`Project configuration not found for project: ${projectId}`);
      }

      const goal = goalOverride || config.goal;
      const context = contextOverride || config.context || '';
      const complexityAnalysis = this.analyzeGoalComplexity(goal, context);

      const htaData = {
        projectId,
        pathName: pathName || 'general',
        created: new Date().toISOString(),
        learningStyle,
        focusAreas,
        goal: config.goal,
        context: config.context || '',
        complexity: complexityAnalysis,
        strategicBranches: [],
        frontierNodes: [],
        completedNodes: [],
        collaborative_sessions: [],
        hierarchyMetadata: {
          total_depth: complexityAnalysis.recommended_depth,
          total_branches: 0,
          total_sub_branches: 0,
          total_tasks: 0,
          branch_task_distribution: {}
        },
        generation_context: {
          method: 'deep_hierarchical_ai',
          timestamp: new Date().toISOString(),
          goal: config.goal,
          complexity_score: complexityAnalysis.score,
          awaiting_generation: true
        }
      };

      // Try Claude generation first
      if (this.claudeInterface && globalCircuitBreaker.canExecute()) {
        try {
          const richContext = buildRichContext(config, complexityAnalysis, learningStyle, focusAreas);
          const constraintsPrompt = formatConstraintsForPrompt(config);
          const branchPrompt = this.buildBranchGenerationPrompt(complexityAnalysis, config, focusAreas, learningStyle, richContext, constraintsPrompt);

          const claudeResponse = await globalCircuitBreaker.execute(async () => {
            return await this.claudeInterface.requestIntelligence(branchPrompt, {
              max_tokens: 4000,
              temperature: 0.7,
              system: "You are an expert learning strategist. Generate comprehensive, actionable learning paths."
            });
          });

          if (claudeResponse && claudeResponse.strategic_branches) {
            const frontierNodes = this.transformTasksToFrontierNodes(claudeResponse.strategic_branches, complexityAnalysis);
            htaData.strategicBranches = claudeResponse.strategic_branches;
            htaData.frontierNodes = frontierNodes;
            htaData.hierarchyMetadata.total_tasks = frontierNodes.length;
            htaData.hierarchyMetadata.total_branches = claudeResponse.strategic_branches.length;
            htaData.generation_context.awaiting_generation = false;
          }
        } catch (claudeError) {
          console.warn('Claude generation failed, using skeleton tasks:', claudeError.message);
        }
      }

      // Fallback: Generate skeleton tasks if Claude isn't available or fails
      if (htaData.frontierNodes.length === 0) {
        const skeletonTasks = this.generateSkeletonTasks(complexityAnalysis, config, focusAreas, learningStyle);
        htaData.frontierNodes = skeletonTasks;
        htaData.hierarchyMetadata.total_tasks = skeletonTasks.length;
        htaData.generation_context.awaiting_generation = false;
        htaData.strategicBranches = this.deriveStrategicBranches(skeletonTasks);
      }

      await this.dataPersistence.savePathData(projectId, pathName || 'general', 'hta.json', htaData);

      return {
        success: true,
        content: [{
          type: 'text',
          text: `**HTA Tree Generated Successfully!**\n\n**Goal**: ${htaData.goal}\n**Complexity**: ${htaData.complexity.score}/10 (${htaData.complexity.level})\n**Tasks Generated**: ${htaData.frontierNodes.length}\n**Strategic Branches**: ${htaData.strategicBranches.length}\n\n**Next Steps**: Use \`get_next_task\` to begin your learning journey!\n\n**Tree Structure**:\n${this.formatTreeSummary(htaData)}`
        }],
        tasks_count: htaData.frontierNodes.length,
        complexity: htaData.complexity,
        strategic_branches: htaData.strategicBranches.length
      };
    } catch (error) {
      console.error('HTACore.buildHTATree failed:', error);
      return {
        success: false,
        content: [{
          type: 'text',
          text: `**HTA Tree Generation Failed**\n\nError: ${error.message}\n\nPlease check your project configuration and try again.`
        }],
        error: error.message
      };
    }
  }

  // ===== CORE HTA INTELLIGENCE METHODS =====

  analyzeGoalComplexity(goal, context = '') {
    const goalLower = goal.toLowerCase();
    const contextLower = context.toLowerCase();

    let score = 3; // Base complexity
    let factors = [];

    // Professional/career goals (****)
    if (goalLower.includes('professional') || goalLower.includes('career') || goalLower.includes('job') ||
        goalLower.includes('certification') || goalLower.includes('engineer') || goalLower.includes('developer')) {
      score += 4;
      factors.push('Professional/career goal');
    }

    // Technical complexity (****)
    if (goalLower.includes('full-stack') || goalLower.includes('machine learning') || goalLower.includes('ai') ||
        goalLower.includes('blockchain') || goalLower.includes('cloud') || goalLower.includes('devops')) {
      score += 3;
      factors.push('High technical complexity');
    }

    // Learning scope (****)
    if (goalLower.includes('master') || goalLower.includes('expert') || goalLower.includes('advanced')) {
      score += 2;
      factors.push('Advanced mastery required');
    }

    // Time constraints (+1)
    if (contextLower.includes('urgent') || contextLower.includes('deadline') || contextLower.includes('quickly')) {
      score += 1;
      factors.push('Time pressure');
    }

    // Multiple domains (****)
    const domains = ['frontend', 'backend', 'database', 'design', 'testing', 'deployment'];
    const domainCount = domains.filter(domain => goalLower.includes(domain)).length;
    if (domainCount >= 2) {
      score += Math.min(domainCount, 2);
      factors.push(`Multiple domains (${domainCount})`);
    }

    // Cap at 10
    score = Math.min(score, 10);

    let level, recommended_depth;
    if (score <= 3) {
      level = 'simple';
      recommended_depth = 2;
    } else if (score <= 6) {
      level = 'moderate';
      recommended_depth = 3;
    } else if (score <= 8) {
      level = 'complex';
      recommended_depth = 4;
    } else {
      level = 'expert';
      recommended_depth = 5;
    }

    return {
      score,
      level,
      recommended_depth,
      factors,
      analysis: `Goal complexity: ${score}/10 (${level}). Recommended tree depth: ${recommended_depth} levels.`
    };
  }

  calculateTreeStructure(complexityAnalysis, learningStyle, focusAreas) {
    const { score, recommended_depth } = complexityAnalysis;

    // Calculate branches based on complexity and focus areas
    let branchCount = Math.max(3, Math.min(8, Math.floor(score * 1.2) + focusAreas.length));

    // Adjust for learning style
    if (learningStyle === 'hands-on') {
      branchCount = Math.max(branchCount, 4); // More practical branches
    } else if (learningStyle === 'theoretical') {
      branchCount = Math.max(branchCount, 3); // Fewer, deeper branches
    }

    // Calculate tasks per branch
    const baseTasksPerBranch = Math.max(2, Math.floor(10 / branchCount));
    const totalTasks = branchCount * baseTasksPerBranch;

    return {
      recommended_branches: branchCount,
      recommended_depth,
      tasks_per_branch: baseTasksPerBranch,
      total_estimated_tasks: totalTasks,
      structure_rationale: `${branchCount} branches × ${baseTasksPerBranch} tasks = ${totalTasks} total tasks`
    };
  }

  generateSkeletonTasks(complexityAnalysis, config = {}, focusAreas = [], learningStyle = 'mixed') {
    const tasks = [];
    const goal = config.goal || 'Reach goal';
    const strategicBranches = this.generateStrategicBranches(goal, complexityAnalysis, focusAreas);

    let taskId = 1;
    strategicBranches.forEach((branch, branchIndex) => {
      const branchTasks = this.generateBranchTasks(branch, complexityAnalysis, learningStyle, taskId);
      tasks.push(...branchTasks);
      taskId += branchTasks.length;
    });

    return tasks;
  }

  generateStrategicBranches(goal, complexityAnalysis, focusAreas) {
    const branches = [];
    const goalLower = goal.toLowerCase();

    // Always include foundation
    branches.push({
      name: 'Foundation',
      description: 'Build fundamental knowledge and core concepts',
      priority: 1,
      focus: 'theory'
    });

    // Add domain-specific branches based on goal
    if (goalLower.includes('web') || goalLower.includes('frontend')) {
      branches.push({
        name: 'Frontend Development',
        description: 'User interface and client-side development',
        priority: 2,
        focus: 'hands-on'
      });
    }

    if (goalLower.includes('backend') || goalLower.includes('server') || goalLower.includes('api')) {
      branches.push({
        name: 'Backend Development',
        description: 'Server-side logic and data management',
        priority: 3,
        focus: 'hands-on'
      });
    }

    if (goalLower.includes('full-stack') || goalLower.includes('fullstack')) {
      branches.push(
        {
          name: 'Frontend Mastery',
          description: 'Complete frontend development skills',
          priority: 2,
          focus: 'hands-on'
        },
        {
          name: 'Backend Mastery',
          description: 'Complete backend development skills',
          priority: 3,
          focus: 'hands-on'
        },
        {
          name: 'Integration & Deployment',
          description: 'Connecting frontend and backend, deployment strategies',
          priority: 4,
          focus: 'practical'
        }
      );
    }

    // Add focus area branches
    focusAreas.forEach((area, index) => {
      if (!branches.some(b => b.name.toLowerCase().includes(area.toLowerCase()))) {
        branches.push({
          name: `${area.charAt(0).toUpperCase() + area.slice(1)} Specialization`,
          description: `Deep dive into ${area} concepts and applications`,
          priority: 5 + index,
          focus: 'specialized'
        });
      }
    });

    // Always include practical application
    branches.push({
      name: 'Practical Application',
      description: 'Real-world projects and portfolio development',
      priority: 10,
      focus: 'project'
    });

    return branches.slice(0, Math.min(8, complexityAnalysis.recommended_depth + 2));
  }

  generateBranchTasks(branch, complexityAnalysis, learningStyle, startId) {
    const tasks = [];
    const tasksPerBranch = Math.max(2, Math.floor(8 / Math.max(1, complexityAnalysis.recommended_depth - 1)));

    for (let i = 0; i < tasksPerBranch; i++) {
      const difficulty = Math.min(5, Math.max(1, Math.floor(complexityAnalysis.score / 2) + (i * 0.5)));
      const duration = this.calculateTaskDuration(difficulty, learningStyle);

      tasks.push({
        id: `${branch.name.toLowerCase().replace(/\s+/g, '_')}_${startId + i}`,
        title: this.generateTaskTitle(branch, i, tasksPerBranch),
        description: this.generateTaskDescription(branch, i, tasksPerBranch),
        difficulty,
        duration,
        branch: branch.name,
        priority: branch.priority * 100 + i * 10,
        prerequisites: i > 0 ? [`${branch.name.toLowerCase().replace(/\s+/g, '_')}_${startId + i - 1}`] : [],
        learningOutcome: `Progress in ${branch.name}`,
        generated: true
      });
    }

    return tasks;
  }

  generateTaskTitle(branch, taskIndex, totalTasks) {
    const branchName = branch.name;
    const progressTerms = ['Introduction to', 'Exploring', 'Understanding', 'Mastering', 'Advanced'];
    const termIndex = Math.floor((taskIndex / totalTasks) * progressTerms.length);
    const term = progressTerms[Math.min(termIndex, progressTerms.length - 1)];

    return `${term} ${branchName}`;
  }

  generateTaskDescription(branch, taskIndex, totalTasks) {
    const isFirst = taskIndex === 0;
    const isLast = taskIndex === totalTasks - 1;

    if (isFirst) {
      return `Begin your journey with ${branch.name}. ${branch.description}`;
    } else if (isLast) {
      return `Complete your ${branch.name} learning with advanced concepts and practical application`;
    } else {
      return `Continue building your ${branch.name} skills with intermediate concepts`;
    }
  }

  calculateTaskDuration(difficulty, learningStyle) {
    const baseDuration = 25; // minutes
    const difficultyMultiplier = 1 + (difficulty - 1) * 0.3;

    let styleMultiplier = 1;
    if (learningStyle === 'hands-on') {
      styleMultiplier = 1.2; // Hands-on takes longer
    } else if (learningStyle === 'reading') {
      styleMultiplier = 0.8; // Reading is faster
    }

    const duration = Math.round(baseDuration * difficultyMultiplier * styleMultiplier);
    return `${duration} minutes`;
  }

  transformTasksToFrontierNodes(strategicBranches, complexityAnalysis) {
    const frontierNodes = [];
    let taskId = 1;

    strategicBranches.forEach((branch, branchIndex) => {
      if (branch.tasks && Array.isArray(branch.tasks)) {
        branch.tasks.forEach((task, taskIndex) => {
          frontierNodes.push({
            id: `${branch.name.toLowerCase().replace(/\s+/g, '_')}_${taskId}`,
            title: task.title || task.name || `${branch.name} Task ${taskIndex + 1}`,
            description: task.description || `Work on ${branch.name} objectives`,
            difficulty: task.difficulty || Math.min(5, Math.max(1, Math.floor(complexityAnalysis.score / 2))),
            duration: task.duration || this.calculateTaskDuration(task.difficulty || 2, 'mixed'),
            branch: branch.name,
            priority: (branchIndex + 1) * 100 + taskIndex * 10,
            prerequisites: task.prerequisites || [],
            learningOutcome: task.learning_outcome || `Progress in ${branch.name}`,
            generated: true,
            completed: false
          });
          taskId++;
        });
      }
    });

    return frontierNodes;
  }

  deriveStrategicBranches(tasks) {
    const branchMap = new Map();

    tasks.forEach(task => {
      const branchName = task.branch || 'General';
      if (!branchMap.has(branchName)) {
        branchMap.set(branchName, {
          name: branchName,
          description: `Tasks related to ${branchName}`,
          tasks: [],
          priority: task.priority ? Math.floor(task.priority / 100) : 1
        });
      }
      branchMap.get(branchName).tasks.push(task);
    });

    return Array.from(branchMap.values()).sort((a, b) => a.priority - b.priority);
  }

  buildBranchGenerationPrompt(complexityAnalysis, config, focusAreas, learningStyle, richContext, constraintsPrompt) {
    return `Generate a comprehensive HTA (Hierarchical Task Analysis) structure for this learning goal:

**Goal**: ${config.goal}
**Context**: ${config.context || 'Self-directed learning'}
**Learning Style**: ${learningStyle}
**Focus Areas**: ${focusAreas.join(', ') || 'General'}
**Complexity**: ${complexityAnalysis.score}/10 (${complexityAnalysis.level})

${richContext}
${constraintsPrompt}

Generate ${complexityAnalysis.recommended_depth} strategic branches, each with 2-4 specific, actionable tasks.

Return JSON format:
{
  "strategic_branches": [
    {
      "name": "Branch Name",
      "description": "What this branch covers",
      "priority": 1,
      "tasks": [
        {
          "title": "Specific task title",
          "description": "What to do and why",
          "difficulty": 1-5,
          "duration": "25 minutes",
          "prerequisites": [],
          "learning_outcome": "What you'll learn"
        }
      ]
    }
  ]
}`;
  }

  async loadPathHTA(projectId, pathName) {
    try {
      if (pathName === 'general') {
        return await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.HTA);
      } else {
        return await this.dataPersistence.loadPathData(projectId, pathName, FILE_NAMES.HTA);
      }
    } catch (error) {
      return null;
    }
  }

  async getNextTaskFromExistingTree(htaData) {
    const availableTasks = htaData.frontierNodes?.filter(task => !task.completed) || [];
    if (availableTasks.length === 0) {
      return null;
    }

    // Return highest priority available task
    availableTasks.sort((a, b) => (a.priority || 0) - (b.priority || 0));
    return availableTasks[0];
  }

  formatTreeSummary(htaData) {
    const branches = htaData.strategicBranches || [];
    if (branches.length === 0) {
      return 'Tasks organized by priority and difficulty';
    }

    return branches.map((branch, index) =>
      `${index + 1}. **${branch.name}**: ${branch.description || 'Strategic learning branch'}`
    ).join('\n');
  }
}
