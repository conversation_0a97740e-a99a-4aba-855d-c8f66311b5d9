/**
 * MCP Core Module - Consolidated MCP Handlers & Communication
 * Optimized from mcp-handlers.js - Preserves all 12 core tool definitions and handler setup
 */

import { ListToolsRequestSchema, CallToolRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { debugLogger } from '../../modules/utils/debug-logger.js';

export class McpCore {
  constructor(server) {
    this.server = server;
    // Eagerly populate tools for handshake
    const toolDefs = this.getToolDefinitions();
    if (!this.server.capabilities) { this.server.capabilities = {}; }
    this.server.capabilities.tools = Object.fromEntries(toolDefs.map(t => [t.name, t]));
  }

  async setupHandlers() {
    const setupStart = Date.now();
    const debugSetup = (step, data = {}) => {
      const elapsed = Date.now() - setupStart;
      console.error(`[MCP-SETUP-${elapsed}ms] ${step}`);
      if (Object.keys(data).length > 0) {
        console.error(`[MCP-SETUP-${elapsed}ms] Data:`, JSON.stringify(data, null, 2));
      }
    };

    debugSetup('Starting MCP handlers setup...');
    debugLogger.logEvent('MCP_HANDLERS_SETUP_START');

    try {
      // List tools handler
      this.server.setRequestHandler(ListToolsRequestSchema, async () => {
        debugLogger.logEvent('LIST_TOOLS_REQUEST');
        const tools = this.getToolDefinitions();
        debugSetup(`Returning ${tools.length} tools`);
        return { tools };
      });

      // Call tool handler
      this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
        const { name, arguments: args } = request.params;
        debugLogger.logEvent('CALL_TOOL_REQUEST', { toolName: name, hasArgs: !!args });
        debugSetup(`Tool called: ${name}`);

        try {
          const result = await this.handleToolCall(name, args || {});
          debugLogger.logEvent('TOOL_CALL_SUCCESS', { toolName: name });
          return result;
        } catch (error) {
          const err = error instanceof Error ? error : new Error(String(error));
          debugLogger.logCritical('TOOL_CALL_ERROR', {
            toolName: name,
            error: err.message,
            stack: err.stack?.substring(0, 500)
          });
          debugSetup(`❌ Tool ${name} failed: ${err.message}`);
          
          return {
            content: [{
              type: 'text',
              text: `**Tool Error: ${name}**\n\nError: ${err.message}\n\nPlease check your input and try again.`
            }],
            isError: true
          };
        }
      });

      debugSetup('✅ MCP handlers setup complete');
      debugLogger.logEvent('MCP_HANDLERS_SETUP_COMPLETE');

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      debugSetup('❌ MCP handlers setup failed', { error: err.message });
      debugLogger.logCritical('MCP_HANDLERS_SETUP_ERROR', {
        error: err.message,
        stack: err.stack?.substring(0, 500)
      });
      throw err;
    }
  }

  async handleToolCall(toolName, args) {
    // Import the tool router dynamically to avoid circular dependencies
    const { ToolRouter } = await import('../tool-router.js');
    const toolRouter = new ToolRouter();
    
    // Delegate to the existing tool router
    return await toolRouter.handleToolCall(toolName, args);
  }

  getToolDefinitions() {
    return [
      {
        name: 'create_project_forest',
        description: 'Create comprehensive life orchestration project with detailed personal context',
        inputSchema: {
          type: 'object',
          properties: {
            project_id: {
              type: 'string',
              description: 'Unique project identifier (e.g. "dream_project_alpha")'
            },
            goal: {
              type: 'string',
              description: 'Ultimate ambitious goal (what you want to achieve)'
            },
            context: {
              type: 'string',
              description: 'Current life situation and why this goal matters now'
            },
            specific_interests: {
              type: 'array',
              items: { type: 'string' },
              description: 'Optional: Specific things you want to be able to do (e.g. "play Let It Be on piano", "build a personal website"). Leave empty if you\'re not sure yet - the system will help you discover interests.'
            },
            learning_paths: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  path_name: {
                    type: 'string',
                    description: 'Name of the learning path (e.g. "saxophone", "piano", "theory")'
                  },
                  priority: {
                    type: 'string',
                    enum: ['high', 'medium', 'low'],
                    description: 'Relative priority of this path'
                  },
                  interests: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Specific interests for this path'
                  }
                },
                required: ['path_name']
              },
              description: 'Optional: Define separate learning paths within your goal for isolated focus (e.g. separate piano and saxophone paths)'
            },
            constraints: {
              type: 'object',
              properties: {
                time_constraints: {
                  type: 'string',
                  description: 'Available time slots, busy periods, commitments'
                },
                energy_patterns: {
                  type: 'string',
                  description: 'When you have high/low energy, physical limitations'
                },
                focus_variability: {
                  type: 'string',
                  description: 'How your focus and attention vary (e.g. "consistent daily", "varies with interest", "unpredictable energy levels")'
                },
                financial_constraints: {
                  type: 'string',
                  description: 'Budget limitations affecting learning resources'
                },
                location_constraints: {
                  type: 'string',
                  description: 'Home setup, workspace limitations, travel requirements'
                }
              }
            },
            existing_credentials: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  subject_area: {
                    type: 'string',
                    description: 'What field/subject'
                  },
                  credential_type: {
                    type: 'string',
                    description: 'Degree, certificate, course, etc.'
                  },
                  level: {
                    type: 'string',
                    description: 'Beginner, intermediate, advanced, expert'
                  },
                  relevance_to_goal: {
                    type: 'string',
                    description: 'How this relates to your new goal'
                  }
                }
              },
              description: 'All existing education, certificates, and relevant experience'
            },
            current_habits: {
              type: 'object',
              properties: {
                good_habits: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Existing positive habits to maintain/build on'
                },
                bad_habits: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Habits you want to replace or minimize'
                },
                habit_goals: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'New habits you want to build alongside learning'
                }
              }
            },
            life_structure_preferences: {
              type: 'object',
              properties: {
                wake_time: {
                  type: 'string',
                  description: 'Preferred wake time (e.g. "6:00 AM")'
                },
                sleep_time: {
                  type: 'string',
                  description: 'Preferred sleep time (e.g. "10:30 PM")'
                },
                meal_times: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Preferred meal schedule'
                },
                focus_duration: {
                  type: 'string',
                  description: 'Preferred focus session length (e.g. "25 minutes", "2 hours", "until natural break", "flexible", "variable")'
                },
                break_preferences: {
                  type: 'string',
                  description: 'How often and what type of breaks you need'
                },
                transition_time: {
                  type: 'string',
                  description: 'Time needed between activities'
                }
              }
            },
            urgency_level: {
              type: 'string',
              enum: ['low', 'medium', 'high', 'critical'],
              description: 'How urgently you need to achieve this goal'
            },
            success_metrics: {
              type: 'array',
              items: { type: 'string' },
              description: 'How you will measure success (income, job offers, portfolio pieces, etc.)'
            }
          },
          required: ['project_id', 'goal', 'life_structure_preferences']
        }
      },
      {
        name: 'switch_project_forest',
        description: 'Switch to a different project workspace',
        inputSchema: {
          type: 'object',
          properties: {
            project_id: {
              type: 'string',
              description: 'Project to switch to'
            }
          },
          required: ['project_id']
        }
      },
      {
        name: 'list_projects_forest',
        description: 'Show all project workspaces',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'get_active_project_forest',
        description: 'Show current active project',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'build_hta_tree_forest',
        description: 'Build strategic HTA framework for a specific learning path',
        inputSchema: {
          type: 'object',
          properties: {
            path_name: {
              type: 'string',
              description: 'Learning path to build HTA tree for (e.g. "saxophone", "piano"). If not specified, builds for active path or general project.'
            },
            learning_style: {
              type: 'string',
              description: 'Preferred learning approach (visual, hands-on, research-based, etc.)'
            },
            focus_areas: {
              type: 'array',
              items: { type: 'string' },
              description: 'Specific areas to prioritize in the strategy'
            }
          }
        }
      },
      {
        name: 'get_hta_status_forest',
        description: 'View HTA strategic framework for active project',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'generate_daily_schedule_forest',
        description: 'ON-DEMAND: Generate comprehensive gap-free daily schedule when requested by user',
        inputSchema: {
          type: 'object',
          properties: {
            date: {
              type: 'string',
              description: 'YYYY-MM-DD, defaults to today'
            },
            energy_level: {
              type: 'number',
              minimum: 1,
              maximum: 5,
              description: 'Current energy level (affects task difficulty and timing)'
            },
            focus_type: {
              type: 'string',
              enum: ['learning', 'building', 'networking', 'habits', 'mixed'],
              description: 'Type of work to prioritize today'
            },
            available_hours: {
              type: 'string',
              description: 'Comma-separated list of hours to prioritize (e.g. "9,10,11,14,15")'
            },
            schedule_request_context: {
              type: 'string',
              description: 'User context about why they need a schedule now (e.g. "planning tomorrow", "need structure today")'
            }
          }
        }
      },
      {
        name: 'complete_block_forest',
        description: 'Complete time block and capture insights for active project',
        inputSchema: {
          type: 'object',
          properties: {
            block_id: { type: 'string' },
            outcome: {
              type: 'string',
              description: 'What happened? Key insights?'
            },
            energy_level: {
              type: 'number',
              minimum: 1,
              maximum: 5,
              description: 'Energy after completion'
            },
            learned: {
              type: 'string',
              description: 'What specific knowledge or skills did you gain?'
            },
            next_questions: {
              type: 'string',
              description: 'What questions emerged? What do you need to learn next?'
            },
            difficulty_rating: {
              type: 'number',
              minimum: 1,
              maximum: 5,
              description: 'How difficult was this task? (1=too easy, 5=too hard)'
            },
            breakthrough: {
              type: 'boolean',
              description: 'Major insight or breakthrough?'
            }
          },
          required: ['block_id', 'outcome', 'energy_level']
        }
      },
      {
        name: 'current_status_forest',
        description: 'Show todays progress and next action for active project',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'evolve_strategy_forest',
        description: 'Analyze patterns and evolve the approach for active project',
        inputSchema: {
          type: 'object',
          properties: {
            feedback: {
              type: 'string',
              description: 'What\'s working? What\'s not? What needs to change?'
            }
          }
        }
      },
      {
        name: 'get_next_task_forest',
        description: 'Get the single most logical next task based on current progress and context',
        inputSchema: {
          type: 'object',
          properties: {
            energy_level: {
              type: 'number',
              minimum: 1,
              maximum: 5,
              description: 'Current energy level to match appropriate task difficulty'
            },
            time_available: {
              type: 'string',
              description: 'Time available for the task (e.g. "30 minutes", "1 hour")'
            },
            context_from_memory: {
              type: 'string',
              description: 'Optional context retrieved from Memory MCP about recent progress/insights'
            }
          }
        }
      },
      {
        name: 'sync_forest_memory_forest',
        description: 'Sync current Forest state to memory for context awareness',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      }
    ];
  }
}
