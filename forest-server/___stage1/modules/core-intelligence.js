/**
 * Core Intelligence Module - Consolidated Reasoning Engine
 * Optimized from reasoning-engine.js - Preserves deductive reasoning and context analysis
 */

import { FILE_NAMES, DEFAULT_PATHS, THRESHOLDS } from './memory-sync.js';

export class CoreIntelligence {
  constructor(dataPersistence, projectManagement) {
    this.dataPersistence = dataPersistence;
    this.projectManagement = projectManagement;
  }

  async analyzeReasoning(includeDetailedAnalysis = true) {
    try {
      const projectId = await this.projectManagement.requireActiveProject();

      // Generate logical deductions from completion patterns
      const deductions = await this.generateLogicalDeductions(projectId);

      // Generate pacing context
      const pacingContext = await this.generatePacingContext(projectId);

      // Combine analysis
      const analysis = {
        deductions,
        pacingContext,
        recommendations: this.generateRecommendations(deductions, pacingContext),
        timestamp: new Date().toISOString()
      };

      const reportText = this.formatReasoningReport(analysis, includeDetailedAnalysis);

      return {
        content: [{
          type: 'text',
          text: reportText
        }],
        reasoning_analysis: analysis
      };
    } catch (error) {
      console.error('CoreIntelligence.analyzeReasoning failed:', error);
      return {
        content: [{
          type: 'text',
          text: `**Reasoning Analysis Failed**\n\nError: ${error.message}\n\nPlease check your project configuration and try again.`
        }],
        error: error.message
      };
    }
  }

  async generateLogicalDeductions(projectId) {
    const config = await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.CONFIG);
    const learningHistory = await this.loadLearningHistory(projectId, config?.activePath || DEFAULT_PATHS.GENERAL);

    const deductions = [];

    if (!learningHistory?.completedTopics?.length) {
      return [{ type: 'insufficient_data', insight: 'Need more completed tasks for pattern analysis' }];
    }

    const completedTopics = learningHistory.completedTopics;

    // Analyze difficulty progression
    const difficultyProgression = this.analyzeDifficultyProgression(completedTopics);
    if (difficultyProgression.insight) {
      deductions.push({
        type: 'difficulty_pattern',
        insight: difficultyProgression.insight,
        evidence: difficultyProgression.evidence
      });
    }

    // Analyze engagement patterns
    const engagementPattern = this.analyzeEngagementPattern(completedTopics);
    if (engagementPattern.insight) {
      deductions.push({
        type: 'engagement_pattern',
        insight: engagementPattern.insight,
        evidence: engagementPattern.evidence
      });
    }

    // Analyze learning velocity
    const velocityPattern = this.analyzeVelocityPattern(completedTopics);
    if (velocityPattern.insight) {
      deductions.push({
        type: 'velocity_pattern',
        insight: velocityPattern.insight,
        evidence: velocityPattern.evidence
      });
    }

    // Analyze breakthrough patterns
    const breakthroughPattern = this.analyzeBreakthroughPattern(completedTopics);
    if (breakthroughPattern.insight) {
      deductions.push({
        type: 'breakthrough_pattern',
        insight: breakthroughPattern.insight,
        evidence: breakthroughPattern.evidence
      });
    }

    return deductions;
  }

  analyzeDifficultyProgression(completedTopics) {
    const difficulties = completedTopics.map(t => t.difficulty || 3).filter(d => d > 0);
    if (difficulties.length < 3) return { insight: null };

    const recent = difficulties.slice(-5);
    const earlier = difficulties.slice(0, -5);
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const earlierAvg = earlier.length > 0 ? earlier.reduce((a, b) => a + b, 0) / earlier.length : recentAvg;

    let insight = null;
    let evidence = [];

    if (recentAvg > earlierAvg + 1) {
      insight = 'Difficulty escalation detected - you\'re taking on increasingly challenging tasks';
      evidence = [`Recent avg difficulty: ${recentAvg.toFixed(1)}`, `Earlier avg: ${earlierAvg.toFixed(1)}`];
    } else if (recentAvg < earlierAvg - 1) {
      insight = 'Difficulty reduction pattern - focusing on consolidation or easier tasks';
      evidence = [`Recent avg difficulty: ${recentAvg.toFixed(1)}`, `Earlier avg: ${earlierAvg.toFixed(1)}`];
    } else if (Math.max(...recent) - Math.min(...recent) < 1) {
      insight = 'Consistent difficulty level - maintaining steady challenge';
      evidence = [`Difficulty range: ${Math.min(...recent)}-${Math.max(...recent)}`];
    }

    return { insight, evidence };
  }

  analyzeEngagementPattern(completedTopics) {
    const engagementScores = completedTopics.map(t => {
      const learned = (t.learned || '').length;
      const questions = (t.nextQuestions || '').length;
      return Math.min(10, (learned + questions) / 20); // Normalize to 0-10
    });

    if (engagementScores.length < 3) return { insight: null };

    const avgEngagement = engagementScores.reduce((a, b) => a + b, 0) / engagementScores.length;
    const recent = engagementScores.slice(-3);
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;

    let insight = null;
    let evidence = [];

    if (avgEngagement > 7) {
      insight = 'High engagement pattern - consistently deep learning';
      evidence = [`Average engagement: ${avgEngagement.toFixed(1)}/10`];
    } else if (recentAvg > avgEngagement + 2) {
      insight = 'Increasing engagement - recent tasks showing deeper involvement';
      evidence = [`Recent engagement: ${recentAvg.toFixed(1)}`, `Overall avg: ${avgEngagement.toFixed(1)}`];
    } else if (avgEngagement < THRESHOLDS.LOW_ENGAGEMENT) {
      insight = 'Low engagement pattern - consider adjusting task types or difficulty';
      evidence = [`Average engagement: ${avgEngagement.toFixed(1)}/10`];
    }

    return { insight, evidence };
  }

  analyzeVelocityPattern(completedTopics) {
    const now = Date.now();
    const recentTasks = completedTopics.filter(t => {
      const taskDate = new Date(t.completedAt || t.timestamp || now);
      return (now - taskDate.getTime()) < (THRESHOLDS.RECENT_DAYS * 24 * 60 * 60 * 1000);
    });

    if (recentTasks.length < THRESHOLDS.MIN_TASKS_FOR_ANALYSIS) return { insight: null };

    const velocityScore = recentTasks.length / THRESHOLDS.RECENT_DAYS;
    let insight = null;
    let evidence = [`${recentTasks.length} tasks in last ${THRESHOLDS.RECENT_DAYS} days`];

    if (velocityScore > 1) {
      insight = 'High velocity learning - completing multiple tasks per day';
    } else if (velocityScore > 0.5) {
      insight = 'Steady learning pace - consistent task completion';
    } else {
      insight = 'Slower learning pace - consider smaller tasks or addressing barriers';
    }

    return { insight, evidence };
  }

  analyzeBreakthroughPattern(completedTopics) {
    const breakthroughs = completedTopics.filter(t => t.breakthrough);
    if (breakthroughs.length === 0) return { insight: null };

    const breakthroughRate = breakthroughs.length / completedTopics.length;
    let insight = null;
    let evidence = [`${breakthroughs.length} breakthroughs out of ${completedTopics.length} tasks`];

    if (breakthroughRate > 0.3) {
      insight = 'High breakthrough rate - excellent learning momentum';
    } else if (breakthroughRate > 0.1) {
      insight = 'Regular breakthroughs - good learning progress';
    } else {
      insight = 'Few breakthroughs - consider exploring new approaches or increasing challenge';
    }

    return { insight, evidence };
  }

  async generatePacingContext(projectId) {
    const config = await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.CONFIG);
    const urgencyLevel = config?.urgency_level || 'medium';
    const createdDate = new Date(config?.created_at || Date.now());
    const daysSinceStart = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));

    const htaData = await this.loadHTA(projectId, config?.activePath || DEFAULT_PATHS.GENERAL);
    const progress = this.calculateProgress(htaData);

    const pacingAnalysis = this.analyzePacing(urgencyLevel, daysSinceStart, progress);

    return {
      urgencyLevel,
      daysSinceStart,
      progress,
      pacingAnalysis,
      recommendations: this.generatePacingRecommendations(pacingAnalysis, urgencyLevel)
    };
  }

  calculateProgress(htaData) {
    if (!htaData?.frontierNodes?.length) return 0;
    
    const completed = htaData.frontierNodes.filter(task => task.completed).length;
    return Math.round((completed / htaData.frontierNodes.length) * 100);
  }

  analyzePacing(urgencyLevel, daysSinceStart, progress) {
    const expectedProgress = this.getExpectedProgress(urgencyLevel, daysSinceStart);
    const progressDelta = progress - expectedProgress;

    let status = 'on_track';
    let message = 'Progress is aligned with expectations';

    if (progressDelta > 20) {
      status = 'ahead';
      message = 'Excellent progress - ahead of expected pace';
    } else if (progressDelta < -20) {
      status = 'behind';
      message = 'Progress is behind expected pace';
    } else if (progressDelta < -10) {
      status = 'slightly_behind';
      message = 'Progress is slightly behind expectations';
    }

    return {
      status,
      message,
      expectedProgress,
      actualProgress: progress,
      progressDelta
    };
  }

  getExpectedProgress(urgencyLevel, daysSinceStart) {
    const urgencyMultipliers = {
      low: 0.5,
      medium: 1.0,
      high: 1.5,
      critical: 2.0
    };

    const baseProgressPerDay = 2; // 2% per day baseline
    const multiplier = urgencyMultipliers[urgencyLevel] || 1.0;
    
    return Math.min(100, daysSinceStart * baseProgressPerDay * multiplier);
  }

  generateRecommendations(deductions, pacingContext) {
    const recommendations = [];

    // Pacing-based recommendations
    if (pacingContext.pacingAnalysis.status === 'behind') {
      recommendations.push('Consider increasing task frequency or reducing task complexity');
    } else if (pacingContext.pacingAnalysis.status === 'ahead') {
      recommendations.push('Excellent pace - consider increasing task complexity or exploring advanced topics');
    }

    // Deduction-based recommendations
    deductions.forEach(deduction => {
      switch (deduction.type) {
        case 'engagement_pattern':
          if (deduction.insight.includes('Low engagement')) {
            recommendations.push('Try varying task types or adjusting difficulty to increase engagement');
          }
          break;
        case 'difficulty_pattern':
          if (deduction.insight.includes('reduction')) {
            recommendations.push('Consider gradually increasing task difficulty to maintain growth');
          }
          break;
        case 'velocity_pattern':
          if (deduction.insight.includes('Slower')) {
            recommendations.push('Break down tasks into smaller chunks or address potential barriers');
          }
          break;
        case 'breakthrough_pattern':
          if (deduction.insight.includes('Few breakthroughs')) {
            recommendations.push('Explore new learning approaches or increase challenge level');
          }
          break;
      }
    });

    return recommendations.length > 0 ? recommendations : ['Continue with current approach - patterns look healthy'];
  }

  generatePacingRecommendations(pacingAnalysis, urgencyLevel) {
    const recommendations = [];

    if (pacingAnalysis.status === 'behind') {
      if (urgencyLevel === 'critical') {
        recommendations.push('URGENT: Significantly increase daily task completion');
        recommendations.push('Consider simplifying tasks or getting additional support');
      } else {
        recommendations.push('Increase task frequency or reduce complexity');
        recommendations.push('Identify and address any blocking factors');
      }
    } else if (pacingAnalysis.status === 'ahead') {
      recommendations.push('Great progress! Consider increasing task complexity');
      recommendations.push('Explore advanced topics or additional learning paths');
    }

    return recommendations;
  }

  // ===== UTILITY METHODS =====

  async loadLearningHistory(projectId, pathName) {
    try {
      const historyData = pathName === DEFAULT_PATHS.GENERAL
        ? await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.LEARNING_HISTORY)
        : await this.dataPersistence.loadPathData(projectId, pathName, FILE_NAMES.LEARNING_HISTORY);

      return historyData || { completedTopics: [] };
    } catch (error) {
      console.warn('Failed to load learning history:', error.message);
      return { completedTopics: [] };
    }
  }

  async loadHTA(projectId, pathName) {
    try {
      const htaData = pathName === DEFAULT_PATHS.GENERAL
        ? await this.dataPersistence.loadProjectData(projectId, FILE_NAMES.HTA)
        : await this.dataPersistence.loadPathData(projectId, pathName, FILE_NAMES.HTA);

      return htaData;
    } catch (error) {
      console.warn('Failed to load HTA data:', error.message);
      return null;
    }
  }

  formatReasoningReport(analysis, includeDetailedAnalysis) {
    const { deductions, pacingContext, recommendations } = analysis;

    let report = '# 🧠 Reasoning Analysis Report\n\n';

    // Pacing Summary
    report += `## 📊 Progress Pacing\n`;
    report += `**Status**: ${pacingContext.pacingAnalysis.status.replace('_', ' ').toUpperCase()}\n`;
    report += `**Progress**: ${pacingContext.progress}% (Expected: ${pacingContext.pacingAnalysis.expectedProgress}%)\n`;
    report += `**Days Active**: ${pacingContext.daysSinceStart}\n`;
    report += `**Urgency Level**: ${pacingContext.urgencyLevel}\n\n`;

    // Key Insights
    if (deductions.length > 0) {
      report += `## 🔍 Key Insights\n`;
      deductions.forEach((deduction, index) => {
        report += `**${index + 1}. ${deduction.type.replace('_', ' ').toUpperCase()}**\n`;
        report += `${deduction.insight}\n`;
        if (includeDetailedAnalysis && deduction.evidence?.length > 0) {
          report += `*Evidence: ${deduction.evidence.join(', ')}*\n`;
        }
        report += '\n';
      });
    } else {
      report += `## 🔍 Key Insights\n`;
      report += `Insufficient data for pattern analysis. Complete more tasks to unlock insights.\n\n`;
    }

    // Recommendations
    report += `## 💡 Recommendations\n`;
    recommendations.forEach((rec, index) => {
      report += `${index + 1}. ${rec}\n`;
    });

    // Pacing Recommendations
    if (pacingContext.recommendations?.length > 0) {
      report += `\n**Pacing Recommendations:**\n`;
      pacingContext.recommendations.forEach((rec, index) => {
        report += `• ${rec}\n`;
      });
    }

    report += `\n---\n*Analysis generated at ${new Date(analysis.timestamp).toLocaleString()}*`;

    return report;
  }
}
