/**
 * Core Initialization Module - Streamlined Startup and Initialization Logic
 * Handles initialization of all consolidated modules with validation
 */

import { Stage1CoreServer } from './core-server.js';
const logger = console;
const debugLogger = console;

export class CoreInitialization {
  constructor(options = {}) {
    this.options = options;
    this.server = null;
    this.logger = logger;
    this.debugLogger = debugLogger;
    this.initializationPhases = [];

    logger.info('[CoreInitialization] Initialized with consolidated architecture');
  }

  /**
   * Initialize the Forest system with all consolidated modules
   */
  async initialize() {
    const startTime = Date.now();

    try {
      logger.info('[CoreInitialization] Starting Forest system initialization...');

      // Phase 1: Core Infrastructure
      await this.initializePhase('Core Infrastructure', async () => {
        this.server = new Stage1CoreServer(this.options);
        await this.server.initialize();
      });

      // Phase 2: Module Validation
      await this.initializePhase('Module Validation', async () => {
        await this.validateConsolidatedModules();
      });

      // Phase 3: Health Checks
      await this.initializePhase('Health Checks', async () => {
        await this.performHealthChecks();
      });

      const duration = Date.now() - startTime;
      logger.info('[CoreInitialization] Forest system initialized successfully', {
        duration: `${duration}ms`,
        phases: this.initializationPhases.length
      });

      return this.server;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('[CoreInitialization] Forest system initialization failed', {
        error: error.message,
        duration: `${duration}ms`,
        completedPhases: this.initializationPhases.length
      });
      throw error;
    }
  }

  async initializePhase(phaseName, phaseFunction) {
    const phaseStart = Date.now();

    try {
      logger.debug(`[CoreInitialization] Starting phase: ${phaseName}`);

      await phaseFunction();

      const phaseDuration = Date.now() - phaseStart;
      this.initializationPhases.push({
        name: phaseName,
        duration: phaseDuration,
        success: true
      });

      logger.debug(`[CoreInitialization] Phase completed: ${phaseName}`, {
        duration: `${phaseDuration}ms`
      });
    } catch (error) {
      const phaseDuration = Date.now() - phaseStart;
      this.initializationPhases.push({
        name: phaseName,
        duration: phaseDuration,
        success: false,
        error: error.message
      });

      logger.error(`[CoreInitialization] Phase failed: ${phaseName}`, {
        error: error.message,
        duration: `${phaseDuration}ms`
      });

      throw new Error(`Initialization phase '${phaseName}' failed: ${error.message}`);
    }
  }

  /**
   * Validate all consolidated modules are properly initialized
   */
  async validateConsolidatedModules() {
    if (!this.server) {
      throw new Error('Server not initialized');
    }

    const requiredModules = [
      'dataPersistence',
      'projectManagement',
      'htaCore',
      'taskStrategyCore',
      'coreIntelligence',
      'memorySync',
      'mcpCore'
    ];

    const validationResults = [];

    for (const moduleName of requiredModules) {
      try {
        const module = this.server[moduleName];
        if (!module) {
          throw new Error(`Module ${moduleName} not found`);
        }

        // Basic validation - check if module has expected methods
        const expectedMethods = this.getExpectedMethods(moduleName);
        for (const method of expectedMethods) {
          if (typeof module[method] !== 'function') {
            throw new Error(`Module ${moduleName} missing method: ${method}`);
          }
        }

        validationResults.push({
          module: moduleName,
          status: 'valid',
          methods: expectedMethods.length
        });

        logger.debug(`[CoreInitialization] Module validated: ${moduleName}`);
      } catch (error) {
        validationResults.push({
          module: moduleName,
          status: 'invalid',
          error: error.message
        });

        throw new Error(`Module validation failed for ${moduleName}: ${error.message}`);
      }
    }

    logger.info('[CoreInitialization] All consolidated modules validated', {
      moduleCount: validationResults.length,
      validModules: validationResults.filter(r => r.status === 'valid').length
    });

    return validationResults;
  }

  getExpectedMethods(moduleName) {
    const methodMap = {
      dataPersistence: ['saveProjectData', 'loadProjectData', 'ensureDataDir', 'clearCache'],
      projectManagement: ['createProject', 'switchProject', 'listProjects', 'getActiveProject'],
      htaCore: ['buildHtaTree', 'getHtaStatus', 'generateStrategicBranches'],
      taskStrategyCore: ['getNextTask', 'handleBlockCompletion', 'evolveStrategy'],
      coreIntelligence: ['analyzeReasoning', 'generateLogicalDeductions'],
      memorySync: ['syncForestMemory', 'queueSync', 'getSyncStatus'],
      mcpCore: ['setupHandlers', 'handleToolCall', 'getToolDefinitions']
    };

    return methodMap[moduleName] || [];
  }

  async performHealthChecks() {
    const healthChecks = [];

    try {
      // Check data directory accessibility
      await this.server.dataPersistence.ensureDataDir();
      healthChecks.push({ check: 'data_directory', status: 'healthy' });

      // Check MCP server status
      const mcpServer = this.server.getServer();
      if (mcpServer) {
        healthChecks.push({ check: 'mcp_server', status: 'healthy' });
      } else {
        throw new Error('MCP server not available');
      }

      // Check tool definitions
      const toolDefinitions = this.server.mcpCore.getToolDefinitions();
      if (toolDefinitions && toolDefinitions.length >= 12) {
        healthChecks.push({
          check: 'tool_definitions',
          status: 'healthy',
          toolCount: toolDefinitions.length
        });
      } else {
        throw new Error(`Insufficient tool definitions: ${toolDefinitions?.length || 0}/12`);
      }

      logger.info('[CoreInitialization] Health checks passed', {
        checkCount: healthChecks.length,
        healthyChecks: healthChecks.filter(c => c.status === 'healthy').length
      });

      return healthChecks;
    } catch (error) {
      healthChecks.push({
        check: 'health_validation',
        status: 'unhealthy',
        error: error.message
      });

      throw new Error(`Health check failed: ${error.message}`);
    }
  }

  /**
   * Get initialization status and metrics
   */
  getInitializationStatus() {
    return {
      server: this.server ? 'initialized' : 'not_initialized',
      phases: this.initializationPhases,
      totalPhases: this.initializationPhases.length,
      successfulPhases: this.initializationPhases.filter(p => p.success).length,
      failedPhases: this.initializationPhases.filter(p => !p.success).length,
      totalDuration: this.initializationPhases.reduce((sum, p) => sum + p.duration, 0)
    };
  }

  /**
   * Graceful shutdown with cleanup
   */
  async shutdown() {
    try {
      logger.info('[CoreInitialization] Starting graceful shutdown...');

      if (this.server) {
        await this.server.cleanup();
      }

      logger.info('[CoreInitialization] Forest system shutdown complete');
      return true;
    } catch (error) {
      logger.error('[CoreInitialization] Shutdown failed', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get server instance (for external access)
   */
  getServer() {
    return this.server;
  }
}

// import { CleanForestServer } from '../server-modular.js';
// export { CleanForestServer };
