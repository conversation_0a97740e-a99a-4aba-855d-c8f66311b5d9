/**
 * Stage1 Core Server - Consolidated Forest MCP Server
 * Uses the new consolidated module architecture
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { HtaCore } from './modules/hta-core.js';
import { TaskStrategyCore } from './modules/task-strategy-core.js';
import { CoreIntelligence } from './modules/core-intelligence.js';
import { McpCore } from './modules/mcp-core.js';
import { DataPersistence } from './modules/data-persistence.js';
import { ProjectManagement } from './modules/project-management.js';
import { MemorySync } from './modules/memory-sync.js';
import { logger } from '../modules/utils/logger.js';
import { debugLogger } from '../modules/utils/debug-logger.js';

export class Stage1CoreServer {
  constructor(options = {}) {
    this.options = options;
    this.server = new Server(
      {
        name: 'forest-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Initialize consolidated modules
    this.dataPersistence = new DataPersistence(options.dataDir);
    this.projectManagement = new ProjectManagement(this.dataPersistence);
    this.htaCore = new HtaCore(this.dataPersistence);
    this.taskStrategyCore = new TaskStrategyCore(this.dataPersistence);
    this.coreIntelligence = new CoreIntelligence(this.dataPersistence);
    this.memorySync = new MemorySync(this.dataPersistence);
    this.mcpCore = new McpCore(this.server);

    this.logger = logger;
    this.debugLogger = debugLogger;

    logger.info('[Stage1CoreServer] Initialized with consolidated modules');
  }

  async initialize() {
    try {
      logger.info('[Stage1CoreServer] Starting initialization...');

      // Ensure data directory exists
      await this.dataPersistence.ensureDataDir();

      // Setup MCP handlers
      await this.mcpCore.setupHandlers();

      // Initialize tool router with consolidated modules
      this.setupToolRouter();

      logger.info('[Stage1CoreServer] Initialization complete');
      return true;
    } catch (error) {
      logger.error('[Stage1CoreServer] Initialization failed', {
        error: error.message,
        stack: error.stack?.substring(0, 500)
      });
      throw error;
    }
  }

  setupToolRouter() {
    // Create a simple tool router that delegates to the appropriate modules
    this.toolRouter = {
      handleToolCall: async (toolName, args) => {
        try {
          switch (toolName) {
            case 'create_project_forest':
              return await this.projectManagement.createProject(args);

            case 'switch_project_forest':
              return await this.projectManagement.switchProject(args.project_id);

            case 'list_projects_forest':
              return await this.projectManagement.listProjects();

            case 'get_active_project_forest':
              return await this.projectManagement.getActiveProject();

            case 'build_hta_tree_forest':
              return await this.htaCore.buildHtaTree(args);

            case 'get_hta_status_forest':
              return await this.htaCore.getHtaStatus();

            case 'get_next_task_forest':
              return await this.taskStrategyCore.getNextTask(args);

            case 'complete_block_forest':
              return await this.taskStrategyCore.handleBlockCompletion(args);

            case 'evolve_strategy_forest':
              return await this.taskStrategyCore.evolveStrategy(args);

            case 'current_status_forest':
              return await this.getCurrentStatus();

            case 'generate_daily_schedule_forest':
              return await this.generateDailySchedule(args);

            case 'sync_forest_memory_forest':
              return await this.memorySync.syncForestMemory(
                this.projectManagement.getActiveProjectId()
              );

            default:
              throw new Error(`Unknown tool: ${toolName}`);
          }
        } catch (error) {
          logger.error('[Stage1CoreServer] Tool call failed', {
            toolName,
            error: error.message
          });
          throw error;
        }
      }
    };
  }

  async getCurrentStatus() {
    try {
      const activeProjectId = this.projectManagement.getActiveProjectId();
      if (!activeProjectId) {
        return {
          content: [{
            type: 'text',
            text: '**No Active Project** ❌\n\nCreate or switch to a project first.'
          }]
        };
      }

      const projectConfig = await this.dataPersistence.loadProjectData(activeProjectId, 'config.json');
      const htaData = await this.dataPersistence.loadProjectData(activeProjectId, 'hta.json');

      const availableTasks = htaData?.frontierNodes?.length || 0;
      const progress = projectConfig?.progress || 0;

      return {
        content: [{
          type: 'text',
          text: `**Current Status** 📊\n\n` +
                `**Project**: ${projectConfig?.goal || 'Unknown'}\n` +
                `**Progress**: ${progress}%\n` +
                `**Available Tasks**: ${availableTasks}\n` +
                `**Active Path**: ${projectConfig?.activePath || 'general'}\n\n` +
                `Use \`get_next_task_forest\` to continue learning!`
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `**Status Error**: ${error.message}`
        }]
      };
    }
  }

  async generateDailySchedule(args) {
    // Simple daily schedule generation - can be enhanced later
    const activeProjectId = this.projectManagement.getActiveProjectId();
    if (!activeProjectId) {
      return {
        content: [{
          type: 'text',
          text: '**No Active Project** ❌\n\nCreate or switch to a project first.'
        }]
      };
    }

    return {
      content: [{
        type: 'text',
        text: `**Daily Schedule Generation** 📅\n\n` +
              `Schedule generation is available but simplified in Stage1.\n` +
              `Use \`get_next_task_forest\` for immediate task recommendations.`
      }]
    };
  }

  getServer() {
    return this.server;
  }

  async cleanup() {
    try {
      logger.info('[Stage1CoreServer] Starting cleanup...');

      // Clear caches
      this.dataPersistence.clearCache();

      // Clear memory sync queue
      this.memorySync.clearQueue();

      logger.info('[Stage1CoreServer] Cleanup complete');
    } catch (error) {
      logger.error('[Stage1CoreServer] Cleanup failed', {
        error: error.message
      });
    }
  }
}

// Re-export existing server for backward compatibility during transition
import { CleanForestServer } from '../server-modular.js';
export { CleanForestServer };
