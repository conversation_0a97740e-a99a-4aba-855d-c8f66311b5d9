{"summary": {"totalTests": 10, "passedTests": 0, "failedTests": 10, "successRate": "0.0%", "coverageAreas": 0, "totalCoverageAreas": 12, "coveragePercentage": "0.0%"}, "coverage": {"projectCreation": false, "htaTreeBuilding": false, "taskIntelligence": false, "taskSelection": false, "scheduleGeneration": false, "taskCompletion": false, "strategyEvolution": false, "progressTracking": false, "defenseSystem": false, "memoryManagement": false, "errorHandling": false, "edgeCases": false}, "tests": [{"name": "Project Creation Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/project-management.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/project-management.js\"?"}, "timestamp": "2025-06-29T06:31:18.553Z"}, {"name": "HTA Tree Building Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/hta-tree-builder.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/hta-tree-builder.js\"?"}, "timestamp": "2025-06-29T06:31:18.554Z"}, {"name": "Task Intelligence Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/task-intelligence.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/task-intelligence.js\"?"}, "timestamp": "2025-06-29T06:31:18.554Z"}, {"name": "Schedule Generation Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/schedule-generator.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/schedule-generator.js\"?"}, "timestamp": "2025-06-29T06:31:18.555Z"}, {"name": "Task Completion Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/task-completion.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/task-completion.js\"?"}, "timestamp": "2025-06-29T06:31:18.555Z"}, {"name": "Strategy Evolution Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/strategy-evolver.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/strategy-evolver.js\"?"}, "timestamp": "2025-06-29T06:31:18.555Z"}, {"name": "Progress Tracking Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/analytics-tools.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/analytics-tools.js\"?"}, "timestamp": "2025-06-29T06:31:18.555Z"}, {"name": "Defense System Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/context-guard.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/context-guard.js\"?"}, "timestamp": "2025-06-29T06:31:18.555Z"}, {"name": "Memory Management Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/memory-sync.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/memory-sync.js\"?"}, "timestamp": "2025-06-29T06:31:18.555Z"}, {"name": "Edge Cases Module", "passed": false, "details": {"error": "Cannot find module '/Users/<USER>/Downloads/625forest-main/modules/task-logic/task-selector.js' imported from /Users/<USER>/Downloads/625forest-main/test-core-loop-100-coverage.js\nDid you mean to import \"./forest-server/modules/task-logic/task-selector.js\"?"}, "timestamp": "2025-06-29T06:31:18.555Z"}], "timestamp": "2025-06-29T06:31:18.555Z"}