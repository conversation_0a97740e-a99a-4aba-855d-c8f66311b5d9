#!/usr/bin/env node

/**
 * Transcript Mining Test - Test hyper-granular transcript content extraction
 */

import { TranscriptMiner } from './modules/transcript-miner.js';
import { createLearnLogger } from './modules/utils/custom-logger.js';

// Sample transcript data for testing (simulating YouTube transcript format)
const SAMPLE_TRANSCRIPT = [
  { start: 0, duration: 3, text: "Welcome to guitar lesson number one. Today we're going to learn the basics." },
  { start: 3, duration: 4, text: "First, you need to hold the guitar properly. Place it on your right leg." },
  { start: 7, duration: 5, text: "Make sure your back is straight and your left hand is free to move along the neck." },
  { start: 12, duration: 4, text: "Now let's talk about the pick. Hold it between your thumb and index finger." },
  { start: 16, duration: 3, text: "Don't grip it too tightly - this is a common mistake beginners make." },
  { start: 19, duration: 5, text: "The pick should extend about 2-3 millimeters beyond your fingertips." },
  { start: 24, duration: 4, text: "Let's practice the downstroke technique. Start slowly and build up speed." },
  { start: 28, duration: 6, text: "Try strumming the open strings. Listen to the sound - it should be clear and even." },
  { start: 34, duration: 4, text: "If you hear buzzing, check your pick angle. It should be slightly tilted." },
  { start: 38, duration: 5, text: "Practice this exercise for 10 minutes every day. Consistency is key." },
  { start: 43, duration: 4, text: "Next, we'll learn the G major chord. Place your third finger on the third fret." },
  { start: 47, duration: 5, text: "Your second finger goes on the second fret of the A string." },
  { start: 52, duration: 4, text: "And your fourth finger on the third fret of the high E string." },
  { start: 56, duration: 3, text: "Press down firmly but don't use excessive force." },
  { start: 59, duration: 4, text: "Strum from the sixth string down. Avoid hitting the low E string." },
  { start: 63, duration: 5, text: "If any notes sound muted, adjust your finger pressure and position." },
  { start: 68, duration: 4, text: "Common mistake: letting your thumb wrap around the neck. Keep it behind." },
  { start: 72, duration: 6, text: "Practice switching between open strings and the G chord. Do this slowly at first." },
  { start: 78, duration: 4, text: "Use a metronome set to 60 BPM. This will help you develop timing." },
  { start: 82, duration: 5, text: "Remember, muscle memory takes time to develop. Be patient with yourself." }
];

const VIDEO_METADATA = {
  title: "Guitar Basics - Lesson 1: Proper Technique and G Major Chord",
  author: "Guitar Teacher Pro",
  duration: 87,
  language: "en"
};

class TranscriptMiningTest {
  constructor() {
    this.logger = createLearnLogger('TranscriptMiningTest');
    this.transcriptMiner = new TranscriptMiner({
      miningSensitivity: 'high',
      actionThreshold: 0.6
    });
  }

  async run() {
    try {
      console.log(`
🔬 TRANSCRIPT MINING TEST

Testing hyper-granular extraction from video transcripts
Sample: ${VIDEO_METADATA.title}
Duration: ${VIDEO_METADATA.duration} seconds
Transcript segments: ${SAMPLE_TRANSCRIPT.length}

`);

      const miningResults = await this.transcriptMiner.mineTranscript(SAMPLE_TRANSCRIPT, VIDEO_METADATA);
      this.displayMiningResults(miningResults);

    } catch (error) {
      console.error('❌ Transcript mining test failed:', error.message);
      throw error;
    }
  }

  displayMiningResults(results) {
    console.log('🎯 TRANSCRIPT MINING RESULTS\n');
    console.log('=' * 80);

    // Mining metadata
    console.log('📊 MINING METADATA:');
    console.log(`Total Segments: ${results.miningMetadata.totalSegments}`);
    console.log(`Confidence Score: ${results.miningMetadata.confidenceScore}/10`);
    console.log(`Actionability Score: ${results.miningMetadata.actionabilityScore}/10`);
    console.log(`Granularity Level: ${results.miningMetadata.granularityLevel}`);
    console.log('');

    // Step-by-step instructions
    if (results.stepByStepInstructions.length > 0) {
      console.log('📋 STEP-BY-STEP INSTRUCTIONS:');
      results.stepByStepInstructions.forEach((instruction, index) => {
        console.log(`${index + 1}. [${instruction.timestamp}] ${instruction.instruction}`);
        console.log(`   Type: ${instruction.type}`);
        console.log(`   Confidence: ${Math.round(instruction.confidence * 100)}%`);
        if (instruction.context) {
          console.log(`   Context: ${instruction.context.substring(0, 100)}...`);
        }
        console.log('');
      });
    }

    // Specific techniques
    if (results.specificTechniques.length > 0) {
      console.log('🛠️ SPECIFIC TECHNIQUES:');
      results.specificTechniques.forEach((technique, index) => {
        console.log(`${index + 1}. [${technique.timestamp}] ${technique.technique}`);
        console.log(`   Category: ${technique.category}`);
        console.log(`   Confidence: ${Math.round(technique.confidence * 100)}%`);
        console.log('');
      });
    }

    // Tools and equipment
    if (results.toolsAndEquipment.length > 0) {
      console.log('🔧 TOOLS & EQUIPMENT:');
      results.toolsAndEquipment.forEach((tool, index) => {
        console.log(`${index + 1}. [${tool.timestamp}] ${tool.tool}`);
        console.log(`   Category: ${tool.category}`);
        console.log(`   Confidence: ${Math.round(tool.confidence * 100)}%`);
        console.log('');
      });
    }

    // Common mistakes
    if (results.commonMistakes.length > 0) {
      console.log('⚠️ COMMON MISTAKES:');
      results.commonMistakes.forEach((mistake, index) => {
        console.log(`${index + 1}. [${mistake.timestamp}] ${mistake.mistake}`);
        console.log(`   Severity: ${mistake.severity}`);
        console.log(`   Confidence: ${Math.round(mistake.confidence * 100)}%`);
        console.log('');
      });
    }

    // Time-based actions
    if (results.timeBasedActions.length > 0) {
      console.log('⏰ TIME-BASED ACTIONS:');
      results.timeBasedActions.slice(0, 10).forEach((action, index) => {
        console.log(`${index + 1}. [${action.timestamp}] ${action.action}`);
        console.log(`   Type: ${action.actionType}`);
        console.log(`   Duration: ${Math.round(action.duration)}s`);
        console.log(`   Confidence: ${Math.round(action.confidence * 100)}%`);
        console.log('');
      });
    }

    // Practice exercises
    if (results.practiceExercises.length > 0) {
      console.log('🎯 PRACTICE EXERCISES:');
      results.practiceExercises.forEach((exercise, index) => {
        console.log(`${index + 1}. [${exercise.timestamp}] ${exercise.exercise}`);
        console.log(`   Difficulty: ${exercise.difficulty}`);
        console.log(`   Estimated Time: ${exercise.estimatedTime}`);
        console.log(`   Confidence: ${Math.round(exercise.confidence * 100)}%`);
        console.log('');
      });
    }

    // Key terminology
    if (results.keyTerminology.length > 0) {
      console.log('📚 KEY TERMINOLOGY:');
      results.keyTerminology.forEach((term, index) => {
        console.log(`${index + 1}. [${term.timestamp}] ${term.term}`);
        console.log(`   Definition: ${term.definition}`);
        console.log(`   Confidence: ${Math.round(term.confidence * 100)}%`);
        console.log('');
      });
    }

    // Forest integration preview
    console.log('🌲 FOREST INTEGRATION PREVIEW:');
    console.log('This transcript mining enables Forest to generate tasks like:');
    console.log('');

    if (results.stepByStepInstructions.length > 0) {
      console.log('📝 EXAMPLE GENERATED TASKS:');
      results.stepByStepInstructions.slice(0, 3).forEach((instruction, index) => {
        console.log(`Task ${index + 1}: ${instruction.instruction}`);
        console.log(`  - Timestamp: ${instruction.timestamp}`);
        console.log(`  - Type: ${instruction.type}`);
        console.log(`  - Estimated time: 5-15 minutes`);
        console.log(`  - Video reference: Jump to ${instruction.timestamp}`);
        console.log('');
      });
    }

    if (results.practiceExercises.length > 0) {
      console.log('🎯 PRACTICE TASKS:');
      results.practiceExercises.slice(0, 2).forEach((exercise, index) => {
        console.log(`Practice ${index + 1}: ${exercise.exercise}`);
        console.log(`  - Difficulty: ${exercise.difficulty}`);
        console.log(`  - Duration: ${exercise.estimatedTime}`);
        console.log(`  - Video reference: ${exercise.timestamp}`);
        console.log('');
      });
    }

    console.log('🎉 TRANSCRIPT MINING COMPLETE!');
    console.log('');
    console.log('Key Benefits:');
    console.log('✅ Precise timestamps for each action');
    console.log('✅ Context-aware instruction extraction');
    console.log('✅ Technique categorization');
    console.log('✅ Common mistake identification');
    console.log('✅ Practice exercise extraction');
    console.log('✅ Time-based learning progression');
    console.log('');
    console.log('This enables Forest to create highly specific, timestamped tasks');
    console.log('that reference exact moments in the learning video!');
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new TranscriptMiningTest();
  
  test.run().catch(error => {
    console.error('❌ Transcript mining test failed:', error.message);
    process.exit(1);
  });
}
