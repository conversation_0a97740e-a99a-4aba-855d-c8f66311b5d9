{"name": "learn-mcp-server", "version": "1.0.0", "description": "LearnMCP - Standalone MCP server for learning content extraction and summarization", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@modelcontextprotocol/sdk": "latest", "winston": "^3.17.0", "uuid": "^9.0.0", "youtube-transcript": "^1.2.1", "cheerio": "^1.0.0-rc.12", "@mozilla/readability": "^0.5.0", "jsdom": "^24.0.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "jest": "^30.0.1", "prettier": "^3.5.3"}, "keywords": ["mcp", "learning", "content-extraction", "summarization", "forest", "education"], "author": "Forest Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}