{"mcpServers": {"memory": {"command": "C:\\Users\\<USER>\\local-node\\node-v20.14.0-win-x64\\node.exe", "args": ["C:\\Users\\<USER>\\claude-mcp-configs\\forest-server\\servers\\memory-server.js", "C:\\Users\\<USER>\\claude-mcp-configs\\memory.json"], "env": {"FOREST_DATA_DIR": "C:\\Users\\<USER>\\.forest-data"}}, "filesystem": {"command": "C:\\Users\\<USER>\\local-node\\node-v20.14.0-win-x64\\node.exe", "args": ["C:\\Users\\<USER>\\claude-mcp-configs\\forest-server\\servers\\filesystem-server.js", "--allow", "C:\\Users\\<USER>\\claude-mcp-configs", "--allow", "C:\\Users\\<USER>\\.forest-data", "--allow", "C:\\Users\\<USER>\\Documents"], "env": {"FOREST_DATA_DIR": "C:\\Users\\<USER>\\.forest-data"}}, "forest": {"command": "C:\\Users\\<USER>\\local-node\\node-v20.14.0-win-x64\\node.exe", "args": ["C:\\Users\\<USER>\\claude-mcp-configs\\start-server.js"], "env": {"FOREST_DATA_DIR": "C:\\Users\\<USER>\\.forest-data"}}, "sequential-thinking": {"command": "C:\\Users\\<USER>\\local-node\\node-v20.14.0-win-x64\\node.exe", "args": ["C:\\Users\\<USER>\\claude-mcp-configs\\forest-server\\servers\\sequential-thinking-server.js"], "env": {"FOREST_DATA_DIR": "C:\\Users\\<USER>\\.forest-data"}}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}}}