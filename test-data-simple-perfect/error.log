{"timestamp":"2025-06-29T02:57:46.921Z","operation":"createProject","error":"Cannot read properties of undefined (reading 'syncActiveProjectToMemory')","stack":"TypeError: Cannot read properties of undefined (reading 'syncActiveProjectToMemory')\n    at ProjectManagement.createProject (file:///C:/Users/<USER>/claude-mcp-configs/forest-server/forest-server/modules/project-management.js:183:48)\n    at async testSimpleWorkflow (file:///C:/Users/<USER>/claude-mcp-configs/forest-server/simple-perfect-test.js:52:27)","context":{"project_id":"simple-perfect-1751165866907","goal":"Learn basic React development","context":"Beginner developer wanting to learn frontend","life_structure_preferences":{"wake_time":"08:00","sleep_time":"23:00","focus_duration":"25 minutes"}}}
{"timestamp":"2025-06-29T02:57:47.275Z","operation":"getNextTask","error":"Cannot read properties of undefined (reading 'requireActiveProject')","stack":"TypeError: Cannot read properties of undefined (reading 'requireActiveProject')\n    at TaskIntelligence.getNextTask (file:///C:/Users/<USER>/claude-mcp-configs/forest-server/forest-server/modules/task-intelligence.js:23:54)\n    at testSimpleWorkflow (file:///C:/Users/<USER>/claude-mcp-configs/forest-server/simple-perfect-test.js:82:38)","context":{"contextFromMemory":"simple-perfect-1751165866907","energyLevel":"general","timeAvailable":"30 minutes"}}
{"timestamp":"2025-06-29T02:57:47.347Z","operation":"completeBlock","error":"Invalid blockId: must be a non-empty string or number","stack":"Error: Invalid blockId: must be a non-empty string or number\n    at file:///C:/Users/<USER>/claude-mcp-configs/forest-server/forest-server/modules/task-completion.js:116:15\n    at DataPersistence.executeInTransaction (file:///C:/Users/<USER>/claude-mcp-configs/forest-server/forest-server/modules/data-persistence.js:628:28)\n    at TaskCompletion.completeBlock (file:///C:/Users/<USER>/claude-mcp-configs/forest-server/forest-server/modules/task-completion.js:113:39)\n    at testSimpleWorkflow (file:///C:/Users/<USER>/claude-mcp-configs/forest-server/simple-perfect-test.js:101:47)","context":{"outcome":{"outcome":"Successfully completed the task","learned":"Understanding of basic concepts","nextQuestions":["What should I learn next?"],"breakthrough":"Realized the importance of practice","energyLevel":4,"difficultyRating":3}}}
