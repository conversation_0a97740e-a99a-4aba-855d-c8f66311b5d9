{"type": "module", "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js --testPathIgnorePatterns=/__tests__/", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "validate-fixes": "node install-permanent-fixes.js --validate", "repair-regression": "node install-permanent-fixes.js --repair"}, "dependencies": {"@modelcontextprotocol/sdk": "latest", "node-fetch": "^3.3.2", "uuid": "^9.0.0", "winston": "^3.17.0"}, "devDependencies": {"es-jest": "^2.1.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-promise": "^7.2.1", "jest": "^30.0.1", "prettier": "^3.5.3"}, "regressionProtection": {"version": "1.0.0", "installed": "2025-06-29T03:20:13.451Z", "protectedFiles": ["forest-server/modules/hta-bridge.js", "forest-server/modules/cache-cleaner.js", "forest-server/server-modular.js"]}}